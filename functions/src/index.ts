/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import { beforeUserCreated, beforeUserSignedIn } from 'firebase-functions/v2/identity';
import { onCall } from 'firebase-functions/v2/https';
import { onDocumentDeleted } from 'firebase-functions/v2/firestore';
import { logger } from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK
if (admin.apps.length === 0) {
  admin.initializeApp();
}

// Define user roles
export enum UserRole {
  USER = 'user',
  TECHNICIAN = 'technician',
  ADMIN = 'admin'
}

// User document interface
export interface UserDocument {
  uid: string;
  email: string;
  displayName: string | null;
  photoURL: string | null;
  role: UserRole;
  createdAt: admin.firestore.FieldValue;
  updatedAt: admin.firestore.FieldValue;
  isActive: boolean;
  metadata: {
    lastLoginAt: admin.firestore.FieldValue | null;
    emailVerified: boolean;
    creationMethod: 'email' | 'google' | 'other';
  };
}

/**
 * Triggered when a new user is created in Firebase Authentication
 * Creates a corresponding user document in Firestore with default role
 */
export const createUserDocument = beforeUserCreated(async (event) => {
  const user = event.data;
  
  if (!user) {
    logger.error('No user data provided in beforeUserCreated event');
    return;
  }
  
  try {
    logger.info('Creating user document for new user', { uid: user.uid, email: user.email });

    // Determine creation method
    let creationMethod: 'email' | 'google' | 'other' = 'other';
    if (user.providerData?.some(provider => provider.providerId === 'google.com')) {
      creationMethod = 'google';
    } else if (user.providerData?.some(provider => provider.providerId === 'password')) {
      creationMethod = 'email';
    }

    // Create user document data
    const userDoc: UserDocument = {
      uid: user.uid,
      email: user.email || '',
      displayName: user.displayName || null,
      photoURL: user.photoURL || null,
      role: UserRole.USER, // Default role for new users
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      isActive: true,
      metadata: {
        lastLoginAt: admin.firestore.FieldValue.serverTimestamp(),
        emailVerified: user.emailVerified || false,
        creationMethod
      }
    };

    // Create the user document in Firestore
    await admin.firestore()
      .collection('users')
      .doc(user.uid)
      .set(userDoc);

    logger.info('User document created successfully', { 
      uid: user.uid, 
      email: user.email,
      role: UserRole.USER 
    });

  } catch (error) {
    logger.error('Error creating user document', { 
      uid: user.uid, 
      email: user.email, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    
    // Don't throw the error as it would prevent user creation
    // Instead, log it and let the user be created without the document
  }
});

/**
 * Triggered when a user signs in
 * Updates the lastLoginAt timestamp
 */
export const updateUserLoginTime = beforeUserSignedIn(async (event) => {
  const user = event.data;
  
  if (!user) {
    logger.error('No user data provided in beforeUserSignedIn event');
    return;
  }
  
  try {
    // Update last login time
    await admin.firestore()
      .collection('users')
      .doc(user.uid)
      .update({
        'metadata.lastLoginAt': admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

    logger.info('Updated user login time', { uid: user.uid });
  } catch (error) {
    logger.error('Error updating user login time', { 
      uid: user.uid, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

/**
 * Cloud function to get user profile data
 */
export const getUserProfile = onCall(async (request) => {
  const { auth } = request;
  
  if (!auth) {
    throw new Error('Authentication required');
  }

  try {
    const userDoc = await admin.firestore()
      .collection('users')
      .doc(auth.uid)
      .get();

    if (!userDoc.exists) {
      throw new Error('User document not found');
    }

    return userDoc.data();
  } catch (error) {
    logger.error('Error getting user profile', { 
      uid: auth.uid, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    throw error;
  }
});

/**
 * Cloud function to update user role (admin only)
 */
export const updateUserRole = onCall(async (request) => {
  const { auth, data } = request;
  
  if (!auth) {
    throw new Error('Authentication required');
  }

  const { targetUserId, newRole } = data;

  if (!targetUserId || !newRole) {
    throw new Error('Target user ID and new role are required');
  }

  if (!Object.values(UserRole).includes(newRole)) {
    throw new Error('Invalid role specified');
  }

  try {
    // Check if the requesting user is an admin
    const requestingUserDoc = await admin.firestore()
      .collection('users')
      .doc(auth.uid)
      .get();

    if (!requestingUserDoc.exists || requestingUserDoc.data()?.role !== UserRole.ADMIN) {
      throw new Error('Insufficient permissions. Admin role required.');
    }

    // Update the target user's role
    await admin.firestore()
      .collection('users')
      .doc(targetUserId)
      .update({
        role: newRole,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

    logger.info('User role updated', { 
      adminUid: auth.uid, 
      targetUserId, 
      newRole 
    });

    return { success: true, message: 'User role updated successfully' };
  } catch (error) {
    logger.error('Error updating user role', { 
      adminUid: auth.uid, 
      targetUserId, 
      newRole,
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    throw error;
  }
});

/**
 * Clean up user data when a user document is deleted
 */
export const cleanupUserData = onDocumentDeleted('users/{userId}', async (event) => {
  const userId = event.params.userId;
  
  try {
    logger.info('Starting user data cleanup', { userId });

    // Delete all user's checklists
    const checklistsQuery = admin.firestore()
      .collection('checklists')
      .where('userId', '==', userId);

    const checklistsSnapshot = await checklistsQuery.get();
    const batch = admin.firestore().batch();

    checklistsSnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();

    logger.info('User data cleanup completed', { 
      userId, 
      deletedChecklists: checklistsSnapshot.size 
    });

  } catch (error) {
    logger.error('Error during user data cleanup', { 
      userId, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

/**
 * Health check function
 */
export const healthCheck = onCall(async () => {
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  };
});
