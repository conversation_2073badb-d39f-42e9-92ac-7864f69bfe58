"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/auth/auth-provider";
import { UserService } from "@/lib/user-service";
import { EquipmentTagService } from "@/lib/equipment-tag-service";
import { AdminChecklistService, ChecklistWithUser, ChecklistStats } from "@/lib/admin-checklist-service";
import { UserDocument, UserRole, getRoleDisplayName, getRoleColor, canManageUsers } from "@/types/user";
import { EquipmentTag } from "@/types/equipment-tag";
import { calculateChecklistSummary, exportToExcel, exportToPDF } from "@/lib";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { EquipmentTagForm } from "@/components/equipment/equipment-tag-form";
import { EquipmentTagList } from "@/components/equipment/equipment-tag-list";
import { QRCodeDisplay } from "@/components/equipment/qr-code-display";
import { 
  Loader2, 
  Users, 
  Shield, 
  UserCheck, 
  Crown, 
  AlertTriangle, 
  Tag as TagIcon,
  Plus,
  ClipboardCheck,
  Edit,
  Trash2,
  FileSpreadsheet,
  FileText,
  Cloud,
  Lock,
  Search,
  Filter,
  Calendar,
  Building,
  MapPin,
  User,
  Eye,
  ToggleLeft,
  ToggleRight,
  X
} from "lucide-react";

interface RoleChangeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  user: UserDocument | null;
  newRole: UserRole | null;
}

function RoleChangeDialog({ isOpen, onClose, onConfirm, user, newRole }: RoleChangeDialogProps) {
  if (!user || !newRole) return null;

  const currentRoleDisplay = getRoleDisplayName(user.role);
  const newRoleDisplay = getRoleDisplayName(newRole);

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Confirm Role Change
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <div>
              You are about to change the role of <strong>{user.displayName || user.email}</strong>
            </div>
            <div>
              From: <strong>{currentRoleDisplay}</strong> → To: <strong>{newRoleDisplay}</strong>
            </div>
            <div className="text-sm text-muted-foreground">
              This action will immediately change the user&apos;s permissions. Are you sure you want to continue?
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onClose}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} className="bg-red-600 hover:bg-red-700">
            Change Role
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

function AdminPageContent() {
  const router = useRouter();
  const { user, extendedUser } = useAuth();
  const [activeTab, setActiveTab] = useState("users");
  
  // User management state
  const [users, setUsers] = useState<UserDocument[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const [updatingRoles, setUpdatingRoles] = useState<Record<string, boolean>>({});
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    user: UserDocument | null;
    newRole: UserRole | null;
  }>({
    isOpen: false,
    user: null,
    newRole: null,
  });

  // Equipment tagging state
  const [equipmentTags, setEquipmentTags] = useState<EquipmentTag[]>([]);
  const [isLoadingTags, setIsLoadingTags] = useState(false);
  const [showTagForm, setShowTagForm] = useState(false);
  const [editingTag, setEditingTag] = useState<EquipmentTag | null>(null);
  const [viewingQRTag, setViewingQRTag] = useState<EquipmentTag | null>(null);

  // Checklist management state
  const [checklists, setChecklists] = useState<ChecklistWithUser[]>([]);
  const [isLoadingChecklists, setIsLoadingChecklists] = useState(false);
  const [checklistStats, setChecklistStats] = useState<ChecklistStats | null>(null);
  const [selectedChecklists, setSelectedChecklists] = useState<Set<string>>(new Set());
  const [exportingStates, setExportingStates] = useState<Record<string, { pdf: boolean; excel: boolean }>>({});
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<'all' | 'completed' | 'pending'>('all');
  const [viewingChecklist, setViewingChecklist] = useState<ChecklistWithUser | null>(null);
  const [deletingChecklists, setDeletingChecklists] = useState<Set<string>>(new Set());

  // Location-based filtering state
  const [buildingFilter, setBuildingFilter] = useState<string>('all');
  const [locationFilter, setLocationFilter] = useState<string>('all');
  const [clientFilter, setClientFilter] = useState<string>('all');
  const [availableBuildings, setAvailableBuildings] = useState<string[]>([]);
  const [availableLocations, setAvailableLocations] = useState<string[]>([]);
  const [availableClients, setAvailableClients] = useState<string[]>([]);

  // User management functions
  const loadUsers = useCallback(async () => {
    if (!extendedUser) return;

    setIsLoadingUsers(true);
    try {
      const allUsers = await UserService.getAllUsers();
      setUsers(allUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      alert('Failed to load users. Please try again.');
    } finally {
      setIsLoadingUsers(false);
    }
  }, [extendedUser]);

  // Equipment tagging functions
  const loadEquipmentTags = useCallback(async () => {
    setIsLoadingTags(true);
    try {
      const tags = await EquipmentTagService.getAllEquipmentTags();
      setEquipmentTags(tags);
    } catch (error) {
      console.error('Error loading equipment tags:', error);
      alert('Failed to load equipment tags. Please try again.');
    } finally {
      setIsLoadingTags(false);
    }
  }, []);

  // Checklist management functions
  const loadChecklists = useCallback(async () => {
    setIsLoadingChecklists(true);
    try {
      const [allChecklists, stats] = await Promise.all([
        AdminChecklistService.getAllChecklists(),
        AdminChecklistService.getChecklistStats()
      ]);
      setChecklists(allChecklists);
      setChecklistStats(stats);
    } catch (error) {
      console.error('Error loading checklists:', error);
      alert('Failed to load checklists. Please try again.');
    } finally {
      setIsLoadingChecklists(false);
    }
  }, []);

  // Load filter options
  const loadFilterOptions = useCallback(async () => {
    try {
      const [buildings, clients] = await Promise.all([
        AdminChecklistService.getUniqueBuildings(),
        AdminChecklistService.getUniqueClients()
      ]);
      setAvailableBuildings(buildings);
      setAvailableClients(clients);
      
      // Load locations for the selected building or all if no building selected
      const locations = await AdminChecklistService.getUniqueLocations(
        buildingFilter === 'all' ? undefined : buildingFilter
      );
      setAvailableLocations(locations);
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  }, [buildingFilter]);

  useEffect(() => {
    // Check if user is admin
    if (extendedUser && !canManageUsers(extendedUser.role)) {
      router.push('/dashboard');
      return;
    }

    if (extendedUser) {
      loadUsers();
      loadEquipmentTags();
      loadChecklists();
      loadFilterOptions();
    }
  }, [extendedUser, router, loadUsers, loadEquipmentTags, loadChecklists, loadFilterOptions]);

  // Load locations when building filter changes
  useEffect(() => {
    if (availableBuildings.length > 0) {
      loadFilterOptions();
    }
  }, [buildingFilter, loadFilterOptions, availableBuildings.length]);

  const handleRoleChange = (targetUser: UserDocument, newRole: UserRole) => {
    if (targetUser.role === newRole) return;
    
    setConfirmDialog({
      isOpen: true,
      user: targetUser,
      newRole,
    });
  };

  const confirmRoleChange = async () => {
    const { user: targetUser, newRole } = confirmDialog;
    if (!targetUser || !newRole) return;

    setUpdatingRoles(prev => ({ ...prev, [targetUser.uid]: true }));
    
    try {
      await UserService.updateUserRole(targetUser.uid, newRole);
      await loadUsers(); // Refresh the list
      alert(`Successfully updated ${targetUser.displayName || targetUser.email}&apos;s role to ${getRoleDisplayName(newRole)}`);
    } catch (error) {
      console.error('Error updating role:', error);
      alert('Failed to update user role. Please try again.');
    } finally {
      setUpdatingRoles(prev => ({ ...prev, [targetUser.uid]: false }));
      setConfirmDialog({ isOpen: false, user: null, newRole: null });
    }
  };

  const handleTagSuccess = (tag: EquipmentTag) => {
    setShowTagForm(false);
    setEditingTag(null);
    setViewingQRTag(tag); // Show the QR code immediately
    loadEquipmentTags(); // Refresh the list
  };

  const handleEditTag = (tag: EquipmentTag) => {
    setEditingTag(tag);
    setShowTagForm(true);
    setViewingQRTag(null);
  };

  const handleViewQR = (tag: EquipmentTag) => {
    setViewingQRTag(tag);
    setShowTagForm(false);
    setEditingTag(null);
  };

  const handleNewTag = () => {
    setEditingTag(null);
    setShowTagForm(true);
    setViewingQRTag(null);
  };

  const handleCancelForm = () => {
    setShowTagForm(false);
    setEditingTag(null);
  };

  // Checklist management functions
  const handleDeleteChecklist = async (checklistId: string) => {
    setDeletingChecklists(prev => new Set(prev).add(checklistId));
    try {
      const success = await AdminChecklistService.deleteChecklist(checklistId);
      if (success) {
        await loadChecklists();
        alert('Checklist deleted successfully.');
      } else {
        alert('Failed to delete checklist. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting checklist:', error);
      alert('Failed to delete checklist. Please try again.');
    } finally {
      setDeletingChecklists(prev => {
        const updated = new Set(prev);
        updated.delete(checklistId);
        return updated;
      });
    }
  };

  const handleBulkDelete = async () => {
    if (selectedChecklists.size === 0) return;
    
    if (!confirm(`Are you sure you want to delete ${selectedChecklists.size} checklist(s)? This action cannot be undone.`)) {
      return;
    }

    try {
      const results = await AdminChecklistService.bulkDeleteChecklists(Array.from(selectedChecklists));
      if (results.success.length > 0) {
        alert(`Successfully deleted ${results.success.length} checklist(s).`);
      }
      if (results.failed.length > 0) {
        alert(`Failed to delete ${results.failed.length} checklist(s).`);
      }
      setSelectedChecklists(new Set());
      await loadChecklists();
    } catch (error) {
      console.error('Error bulk deleting checklists:', error);
      alert('Failed to delete checklists. Please try again.');
    }
  };

  const handleToggleCompletion = async (checklistId: string, completed: boolean) => {
    if (!user) return;
    
    try {
      const success = await AdminChecklistService.toggleCompletionStatus(checklistId, completed, user.uid);
      if (success) {
        await loadChecklists();
        alert(`Checklist marked as ${completed ? 'completed' : 'pending'}.`);
      } else {
        alert('Failed to update checklist status. Please try again.');
      }
    } catch (error) {
      console.error('Error toggling completion status:', error);
      alert('Failed to update checklist status. Please try again.');
    }
  };

  const handleExportChecklist = async (checklist: ChecklistWithUser, type: 'excel' | 'pdf') => {
    try {
      setExportingStates(prev => ({
        ...prev,
        [checklist.id]: {
          ...prev[checklist.id],
          [type]: true
        }
      }));

      if (type === 'excel') {
        await exportToExcel(checklist);
      } else {
        await exportToPDF(checklist);
      }
    } catch (error) {
      console.error(`Export ${type} failed:`, error);
      alert(`Failed to export ${type.toUpperCase()} file. Please try again.`);
    } finally {
      setExportingStates(prev => ({
        ...prev,
        [checklist.id]: {
          ...prev[checklist.id],
          [type]: false
        }
      }));
    }
  };

  const filteredChecklists = checklists.filter(checklist => {
    const matchesSearch = searchTerm === '' || 
      checklist.generalInfo.tagNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      checklist.generalInfo.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      checklist.userDisplayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      checklist.userEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      checklist.equipmentTag?.tagNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      checklist.equipmentTag?.equipmentName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'completed' && checklist.isCompleted) ||
      (statusFilter === 'pending' && !checklist.isCompleted);

    const matchesBuilding = buildingFilter === 'all' || 
      checklist.equipmentTag?.building === buildingFilter;

    const matchesLocation = locationFilter === 'all' || 
      checklist.equipmentTag?.location === locationFilter;

    const matchesClient = clientFilter === 'all' || 
      checklist.equipmentTag?.clientName === clientFilter;
    
    return matchesSearch && matchesStatus && matchesBuilding && matchesLocation && matchesClient;
  });

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  // Utility functions for users
  const formatUserDate = (timestamp: unknown) => {
    try {
      let date;
      
      if (!timestamp) {
        return 'Unknown';
      }
      
      if (timestamp && typeof timestamp === 'object' && 'seconds' in timestamp) {
        date = new Date((timestamp as { seconds: number }).seconds * 1000);
      } else if (timestamp && typeof timestamp === 'object' && '_seconds' in timestamp) {
        date = new Date((timestamp as { _seconds: number })._seconds * 1000);
      } else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
      } else if (typeof timestamp === 'number') {
        date = new Date(timestamp);
      } else if (timestamp instanceof Date) {
        date = timestamp;
      } else {
        return 'Invalid Date';
      }

      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }

      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date Error';
    }
  };

  // Utility functions
  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return <Crown className="h-4 w-4" />;
      case UserRole.TECHNICIAN:
        return <UserCheck className="h-4 w-4" />;
      case UserRole.USER:
        return <Users className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const getInitials = (name: string | null, email: string) => {
    if (name && name.trim()) {
      return name.trim().split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return email.slice(0, 2).toUpperCase();
  };

  const getUserStats = () => {
    const stats = {
      total: users.length,
      admins: users.filter(u => u.role === UserRole.ADMIN).length,
      technicians: users.filter(u => u.role === UserRole.TECHNICIAN).length,
      users: users.filter(u => u.role === UserRole.USER).length,
      active: users.filter(u => u.isActive).length,
    };
    return stats;
  };

  if (!extendedUser) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!canManageUsers(extendedUser.role)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen space-y-4">
        <Shield className="h-16 w-16 text-red-500" />
        <h1 className="text-2xl font-bold">Access Denied</h1>
        <p className="text-muted-foreground">You don&apos;t have permission to access this page.</p>
        <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
      </div>
    );
  }

  const stats = getUserStats();

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6 sm:h-8 sm:w-8" />
            Admin Panel
          </h1>
          <p className="text-muted-foreground">Manage users and equipment tags</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4 sm:space-y-6">
        <TabsList className="grid w-full grid-cols-3 h-auto">
          <TabsTrigger value="users" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 sm:p-3">
            <Users className="h-4 w-4" />
            <span className="text-xs sm:text-sm">User Management</span>
          </TabsTrigger>
          <TabsTrigger value="equipment" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 sm:p-3">
            <TagIcon className="h-4 w-4" />
            <span className="text-xs sm:text-sm">Equipment Tagging</span>
          </TabsTrigger>
          <TabsTrigger value="checklist" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 p-2 sm:p-3">
            <ClipboardCheck className="h-4 w-4" />
            <span className="text-xs sm:text-sm">Checklist Management</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4 sm:space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl sm:text-2xl font-bold">User Management</h2>
              <p className="text-muted-foreground">Manage user roles and permissions</p>
            </div>
            <Button
              onClick={loadUsers}
              disabled={isLoadingUsers}
              variant="outline"
              className="w-full sm:w-auto"
            >
              {isLoadingUsers ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Refresh
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2 sm:gap-4">
            <Card>
              <CardContent className="p-3 sm:p-4">
                <div className="text-center">
                  <div className="text-lg sm:text-2xl font-bold">{stats.total}</div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Total Users</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-3 sm:p-4">
                <div className="text-center">
                  <div className="text-lg sm:text-2xl font-bold text-red-600">{stats.admins}</div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Admins</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-3 sm:p-4">
                <div className="text-center">
                  <div className="text-lg sm:text-2xl font-bold text-green-600">{stats.technicians}</div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Technicians</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-3 sm:p-4">
                <div className="text-center">
                  <div className="text-lg sm:text-2xl font-bold text-blue-600">{stats.users}</div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Users</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-3 sm:p-4">
                <div className="text-center">
                  <div className="text-lg sm:text-2xl font-bold text-green-500">{stats.active}</div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Active</div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>All Users</CardTitle>
              <CardDescription>
                View and manage user roles. You can promote users between User, Technician, and Admin levels.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0 sm:p-6">
              {isLoadingUsers ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : users.length === 0 ? (
                <div className="text-center p-8 text-muted-foreground">
                  No users found
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="min-w-[200px]">User</TableHead>
                        <TableHead className="hidden md:table-cell">Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead className="hidden sm:table-cell">Status</TableHead>
                        <TableHead className="hidden lg:table-cell">Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((targetUser) => (
                        <TableRow key={targetUser.uid}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-8 w-8 flex-shrink-0">
                                <AvatarImage src={targetUser.photoURL || ''} alt={targetUser.displayName || ''} />
                                <AvatarFallback>
                                  {getInitials(targetUser.displayName, targetUser.email)}
                                </AvatarFallback>
                              </Avatar>
                              <div className="min-w-0 flex-1">
                                <div className="font-medium text-sm">
                                  {targetUser.displayName || 'No name'}
                                  {targetUser.uid === user?.uid && (
                                    <span className="text-xs text-muted-foreground ml-2">(You)</span>
                                  )}
                                </div>
                                {/* Mobile: Show email and status */}
                                <div className="md:hidden text-xs text-muted-foreground">
                                  <div className="truncate">{targetUser.email}</div>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Badge variant={targetUser.isActive ? "default" : "destructive"} className="text-xs">
                                      {targetUser.isActive ? "Active" : "Inactive"}
                                    </Badge>
                                    <span className="hidden sm:inline text-xs">
                                      {formatUserDate(targetUser.createdAt)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell font-mono text-sm">
                            {targetUser.email}
                          </TableCell>
                          <TableCell>
                            <Badge className={`${getRoleColor(targetUser.role)} text-xs`}>
                              {getRoleIcon(targetUser.role)}
                              <span className="ml-1 hidden sm:inline">{getRoleDisplayName(targetUser.role)}</span>
                              <span className="ml-1 sm:hidden">
                                {targetUser.role === 'admin' ? 'A' : targetUser.role === 'technician' ? 'T' : 'U'}
                              </span>
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            <Badge variant={targetUser.isActive ? "default" : "destructive"} className="text-xs">
                              {targetUser.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell text-sm">
                            {formatUserDate(targetUser.createdAt)}
                          </TableCell>
                          <TableCell className="text-right">
                            {targetUser.uid === user?.uid ? (
                              <span className="text-xs text-muted-foreground">Cannot modify self</span>
                            ) : (
                              <div className="flex items-center justify-end gap-1">
                                <Select
                                  value={targetUser.role}
                                  onValueChange={(newRole: UserRole) => handleRoleChange(targetUser, newRole)}
                                  disabled={updatingRoles[targetUser.uid]}
                                >
                                  <SelectTrigger className="w-[100px] sm:w-[140px] h-8">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value={UserRole.USER}>
                                      <div className="flex items-center gap-2">
                                        <Users className="h-4 w-4" />
                                        User
                                      </div>
                                    </SelectItem>
                                    <SelectItem value={UserRole.TECHNICIAN}>
                                      <div className="flex items-center gap-2">
                                        <UserCheck className="h-4 w-4" />
                                        Technician
                                      </div>
                                    </SelectItem>
                                    <SelectItem value={UserRole.ADMIN}>
                                      <div className="flex items-center gap-2">
                                        <Crown className="h-4 w-4" />
                                        Admin
                                      </div>
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                {updatingRoles[targetUser.uid] && (
                                  <Loader2 className="h-4 w-4 animate-spin ml-2" />
                                )}
                              </div>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="equipment" className="space-y-4 sm:space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl sm:text-2xl font-bold">Equipment Tagging</h2>
              <p className="text-muted-foreground">Create and manage equipment QR code tags</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={loadEquipmentTags}
                disabled={isLoadingTags}
                variant="outline"
                className="w-full sm:w-auto"
              >
                {isLoadingTags ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                Refresh
              </Button>
              <Button onClick={handleNewTag} disabled={showTagForm} className="w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                New Tag
              </Button>
            </div>
          </div>

          {/* Stats Card for Equipment Tags */}
          <Card>
            <CardContent className="p-3 sm:p-4">
              <div className="text-center">
                <div className="text-lg sm:text-2xl font-bold">{equipmentTags.length}</div>
                <div className="text-xs sm:text-sm text-muted-foreground">Total Equipment Tags</div>
              </div>
            </CardContent>
          </Card>

          {/* Equipment Tag Form or QR Display */}
          {showTagForm && (
            <EquipmentTagForm
              onSuccess={handleTagSuccess}
              onCancel={handleCancelForm}
              editingTag={editingTag}
            />
          )}

          {viewingQRTag && !showTagForm && (
            <div className="max-w-2xl mx-auto">
              <QRCodeDisplay equipmentTag={viewingQRTag} />
            </div>
          )}

          {/* Equipment Tags List */}
          {!showTagForm && (
            <EquipmentTagList
              tags={equipmentTags}
              onRefresh={loadEquipmentTags}
              onEdit={handleEditTag}
              onViewQR={handleViewQR}
            />
          )}
        </TabsContent>

        <TabsContent value="checklist" className="space-y-4 sm:space-y-6">
          {/* Checklist Management content */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl sm:text-2xl font-bold">Checklist Management</h2>
              <p className="text-muted-foreground">View and manage all inspection checklists across all users</p>
            </div>
            <Button
              onClick={loadChecklists}
              disabled={isLoadingChecklists}
              variant="outline"
              className="w-full sm:w-auto"
            >
              {isLoadingChecklists ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Refresh
            </Button>
          </div>

          {/* Stats Cards */}
          {checklistStats && (
            <div className="grid grid-cols-2 md:grid-cols-6 gap-2 sm:gap-4">
              <Card>
                <CardContent className="p-3 sm:p-4">
                  <div className="text-center">
                    <div className="text-lg sm:text-2xl font-bold">{checklistStats.total}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Total</div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-3 sm:p-4">
                  <div className="text-center">
                    <div className="text-lg sm:text-2xl font-bold text-purple-600">{checklistStats.completed}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Completed</div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-3 sm:p-4">
                  <div className="text-center">
                    <div className="text-lg sm:text-2xl font-bold text-orange-600">{checklistStats.pending}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Pending</div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-3 sm:p-4">
                  <div className="text-center">
                    <div className="text-lg sm:text-2xl font-bold text-green-600">{checklistStats.synced}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Synced</div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-3 sm:p-4">
                  <div className="text-center">
                    <div className="text-lg sm:text-2xl font-bold text-blue-600">{checklistStats.local}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Local</div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-3 sm:p-4">
                  <div className="text-center">
                    <div className="text-lg sm:text-2xl font-bold text-red-600">{checklistStats.conflicts}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Conflicts</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Search and Filter Controls */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by tag, client, equipment, or user..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={statusFilter} onValueChange={(value: 'all' | 'completed' | 'pending') => setStatusFilter(value)}>
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Location-based filters */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Select value={clientFilter} onValueChange={setClientFilter}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="All Clients" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Clients</SelectItem>
                  {availableClients.map((client) => (
                    <SelectItem key={client} value={client}>
                      {client}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={buildingFilter} onValueChange={(value) => {
                setBuildingFilter(value);
                if (value === 'all') {
                  setLocationFilter('all');
                }
              }}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="All Buildings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Buildings</SelectItem>
                  {availableBuildings.map((building) => (
                    <SelectItem key={building} value={building}>
                      {building}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="All Locations" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  {availableLocations.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Selected filters summary and clear */}
            {(buildingFilter !== 'all' || locationFilter !== 'all' || clientFilter !== 'all') && (
              <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
                <span>Active filters:</span>
                {clientFilter !== 'all' && (
                  <Badge variant="secondary" className="gap-1">
                    Client: {clientFilter}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 w-4 h-4"
                      onClick={() => setClientFilter('all')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {buildingFilter !== 'all' && (
                  <Badge variant="secondary" className="gap-1">
                    Building: {buildingFilter}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 w-4 h-4"
                      onClick={() => {
                        setBuildingFilter('all');
                        setLocationFilter('all');
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {locationFilter !== 'all' && (
                  <Badge variant="secondary" className="gap-1">
                    Location: {locationFilter}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 w-4 h-4"
                      onClick={() => setLocationFilter('all')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setClientFilter('all');
                    setBuildingFilter('all');
                    setLocationFilter('all');
                  }}
                  className="text-xs"
                >
                  Clear all filters
                </Button>
              </div>
            )}
            
            {selectedChecklists.size > 0 && (
              <div className="flex gap-2">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  className="w-full sm:w-auto"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected ({selectedChecklists.size})
                </Button>
              </div>
            )}
          </div>

          {/* Checklists Table */}
          <Card>
            <CardHeader>
              <CardTitle>All Checklists</CardTitle>
              <CardDescription>
                Manage inspection checklists from all users. You can view, edit, delete, and change completion status.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0 sm:p-6">
              {isLoadingChecklists ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : filteredChecklists.length === 0 ? (
                <div className="text-center p-8 text-muted-foreground">
                  {checklists.length === 0 ? "No checklists found" : "No checklists match your search criteria"}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[40px] hidden sm:table-cell">
                          <input
                            type="checkbox"
                            checked={selectedChecklists.size === filteredChecklists.length && filteredChecklists.length > 0}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedChecklists(new Set(filteredChecklists.map(c => c.id)));
                              } else {
                                setSelectedChecklists(new Set());
                              }
                            }}
                          />
                        </TableHead>
                        <TableHead className="min-w-[100px]">Tag No.</TableHead>
                        <TableHead className="hidden md:table-cell">Equipment</TableHead>
                        <TableHead className="hidden lg:table-cell">Location</TableHead>
                        <TableHead className="hidden xl:table-cell">Client</TableHead>
                        <TableHead className="hidden sm:table-cell">User</TableHead>
                        <TableHead className="hidden sm:table-cell">Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="hidden md:table-cell">Summary</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredChecklists.map((checklist) => {
                        const summary = calculateChecklistSummary(checklist);
                        const isSelected = selectedChecklists.has(checklist.id);
                        
                        return (
                          <TableRow key={checklist.id}>
                            <TableCell className="hidden sm:table-cell">
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={(e) => {
                                  const updated = new Set(selectedChecklists);
                                  if (e.target.checked) {
                                    updated.add(checklist.id);
                                  } else {
                                    updated.delete(checklist.id);
                                  }
                                  setSelectedChecklists(updated);
                                }}
                              />
                            </TableCell>
                            <TableCell className="font-medium">
                              <div className="flex flex-col">
                                <div className="flex items-center gap-2">
                                  <span className="text-sm font-medium">{checklist.generalInfo.tagNo}</span>
                                  {checklist.isCompleted && (
                                    <Lock className="h-3 w-3 text-purple-600" />
                                  )}
                                </div>
                                {/* Mobile: Show equipment and user info */}
                                <div className="sm:hidden text-xs text-muted-foreground">
                                  <div>{checklist.equipmentTag?.equipmentName}</div>
                                  <div>{checklist.userDisplayName || 'Unknown'}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="hidden md:table-cell">{checklist.equipmentTag?.equipmentName}</TableCell>
                            <TableCell className="hidden lg:table-cell">{checklist.equipmentTag?.location}</TableCell>
                            <TableCell className="hidden xl:table-cell">{checklist.equipmentTag?.clientName}</TableCell>
                            <TableCell className="hidden sm:table-cell">
                              <div className="flex flex-col">
                                <span className="font-medium text-sm">{checklist.userDisplayName || 'Unknown'}</span>
                                <span className="text-xs text-muted-foreground">{checklist.userEmail}</span>
                              </div>
                            </TableCell>
                            <TableCell className="hidden sm:table-cell text-sm">{formatDate(checklist.generalInfo.date)}</TableCell>
                            <TableCell>
                              <Badge variant={checklist.isCompleted ? "default" : "secondary"} className="text-xs">
                                {checklist.isCompleted ? "Done" : "Pending"}
                              </Badge>
                            </TableCell>
                            <TableCell className="hidden md:table-cell">
                              <div className="flex gap-1">
                                <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                                  {summary.totalOk} OK
                                </Badge>
                                {summary.totalFaulty > 0 && (
                                  <Badge variant="outline" className="text-red-600 border-red-600 text-xs">
                                    {summary.totalFaulty} F
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setViewingChecklist(checklist)}
                                  title="View checklist details"
                                  className="h-8 w-8 p-0"
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                                
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleExportChecklist(checklist, 'pdf')}
                                  disabled={exportingStates[checklist.id]?.pdf}
                                  title="Export to PDF"
                                  className="h-8 w-8 p-0 hidden sm:inline-flex"
                                >
                                  {exportingStates[checklist.id]?.pdf ? (
                                    <Loader2 className="h-3 w-3 animate-spin" />
                                  ) : (
                                    <FileText className="h-3 w-3" />
                                  )}
                                </Button>
                                
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleToggleCompletion(checklist.id, !checklist.isCompleted)}
                                  title={checklist.isCompleted ? "Mark as pending" : "Mark as completed"}
                                  className="h-8 w-8 p-0 hidden sm:inline-flex"
                                >
                                  {checklist.isCompleted ? (
                                    <ToggleLeft className="h-3 w-3" />
                                  ) : (
                                    <ToggleRight className="h-3 w-3" />
                                  )}
                                </Button>
                                
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button 
                                      variant="ghost" 
                                      size="sm"
                                      disabled={deletingChecklists.has(checklist.id)}
                                      title="Delete checklist"
                                      className="h-8 w-8 p-0"
                                    >
                                      {deletingChecklists.has(checklist.id) ? (
                                        <Loader2 className="h-3 w-3 animate-spin" />
                                      ) : (
                                        <Trash2 className="h-3 w-3" />
                                      )}
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Delete Checklist</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Are you sure you want to delete this checklist for {checklist.generalInfo.tagNo}? 
                                        This action cannot be undone.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                                      <AlertDialogAction 
                                        onClick={() => handleDeleteChecklist(checklist.id)}
                                        className="bg-red-600 hover:bg-red-700"
                                      >
                                        Delete
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Checklist Viewing Dialog */}
      {viewingChecklist && (
        <AlertDialog open={true} onOpenChange={() => setViewingChecklist(null)}>
          <AlertDialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <ClipboardCheck className="h-5 w-5" />
                Checklist Details - {viewingChecklist.generalInfo.tagNo}
              </AlertDialogTitle>
            </AlertDialogHeader>
            
            <div className="space-y-6">
              {/* General Information */}
              <div>
                <h3 className="text-lg font-semibold mb-3">General Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Client Name</label>
                    <p className="text-sm">{viewingChecklist.generalInfo.clientName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Tag Number</label>
                    <p className="text-sm">{viewingChecklist.generalInfo.tagNo}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Building</label>
                    <p className="text-sm">{viewingChecklist.generalInfo.building}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Location</label>
                    <p className="text-sm">{viewingChecklist.generalInfo.location}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Date</label>
                    <p className="text-sm">{formatDate(viewingChecklist.generalInfo.date)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Inspector</label>
                    <p className="text-sm">{viewingChecklist.generalInfo.inspectedBy}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">User</label>
                    <p className="text-sm">{viewingChecklist.userDisplayName || 'Unknown'}</p>
                    <p className="text-xs text-muted-foreground">{viewingChecklist.userEmail}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Status</label>
                    <Badge variant={viewingChecklist.isCompleted ? "default" : "secondary"} className="mt-1">
                      {viewingChecklist.isCompleted ? "Completed" : "Pending"}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Summary */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Summary</h3>
                <div className="flex gap-2">
                  {(() => {
                    const summary = calculateChecklistSummary(viewingChecklist);
                    return (
                      <>
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          {summary.totalOk} OK
                        </Badge>
                        {summary.totalFaulty > 0 && (
                          <Badge variant="outline" className="text-red-600 border-red-600">
                            {summary.totalFaulty} Faulty
                          </Badge>
                        )}
                        {summary.totalNA > 0 && (
                          <Badge variant="outline" className="text-gray-600 border-gray-600">
                            {summary.totalNA} N/A
                          </Badge>
                        )}
                        {summary.totalMissing > 0 && (
                          <Badge variant="outline" className="text-orange-600 border-orange-600">
                            {summary.totalMissing} Missing
                          </Badge>
                        )}
                      </>
                    );
                  })()}
                </div>
              </div>

              {/* Remarks */}
              {viewingChecklist.remarks && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Remarks</h3>
                  <p className="text-sm bg-muted p-3 rounded">{viewingChecklist.remarks}</p>
                </div>
              )}
            </div>

            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setViewingChecklist(null)}>
                Close
              </AlertDialogCancel>
              <AlertDialogAction asChild>
                <Button onClick={() => handleExportChecklist(viewingChecklist, 'pdf')} variant="outline">
                  <FileText className="h-4 w-4 mr-2" />
                  Export PDF
                </Button>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      <RoleChangeDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog({ isOpen: false, user: null, newRole: null })}
        onConfirm={confirmRoleChange}
        user={confirmDialog.user}
        newRole={confirmDialog.newRole}
      />
    </div>
  );
}

export default function AdminPage() {
  return <AdminPageContent />;
} 