'use client';

import React from 'react';
import { ChecklistData, SyncMetadata } from '@/types/checklist';
import { DashboardAnalytics } from '@/components/dashboard/DashboardAnalytics';

// Sample data for testing charts
const generateSampleData = (): ChecklistData[] => {
  const sampleData: ChecklistData[] = [];
  
  for (let i = 0; i < 15; i++) {
    const date = new Date();
    date.setDate(date.getDate() - (15 - i));
    
    const statuses: Array<SyncMetadata['status']> = ['synced', 'pending', 'local-only', 'error'];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    
    const checklist: ChecklistData = {
      id: `test-${i}`,
      createdAt: date.toISOString(),
      updatedAt: date.toISOString(),
      userId: 'test-user',
      isCompleted: randomStatus === 'synced',
      syncMetadata: {
        status: randomStatus,
        lastLocalUpdateAt: date.toISOString(),
        localVersion: 1,
        lastSyncedAt: randomStatus === 'synced' ? date.toISOString() : undefined,
        cloudDocumentId: randomStatus === 'synced' ? `cloud-${i}` : undefined
      },
      generalInfo: {
        clientName: 'Sample Client',
        building: 'Test Building',
        inspectedBy: 'Test Inspector',
        approvedBy: 'Test Approver',
        date: date.toISOString().split('T')[0],
        ppmAttempt: 1,
        equipmentName: 'ACMV Unit',
        location: 'Ground Floor',
        tagNo: `TAG-${i}`
      },
      mechanicalChecks: {
        beltWearPulleyAlignment: Math.random() > 0.7 ? 'Faulty' : Math.random() > 0.3 ? 'OK' : 'N/A',
        bladeImpellerDamage: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        boltSetScrewTightness: Math.random() > 0.9 ? 'Faulty' : Math.random() > 0.1 ? 'OK' : 'N/A',
        bladeTipClearance: Math.random() > 0.85 ? 'Faulty' : Math.random() > 0.15 ? 'OK' : 'N/A',
        excessiveVibration: Math.random() > 0.75 ? 'Faulty' : Math.random() > 0.25 ? 'OK' : 'N/A',
        fanGuardProtection: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        fanPowerOff: Math.random() > 0.9 ? 'Faulty' : Math.random() > 0.1 ? 'OK' : 'N/A',
        motorOverheating: Math.random() > 0.85 ? 'Faulty' : Math.random() > 0.15 ? 'OK' : 'N/A',
        rotationDirection: Math.random() > 0.7 ? 'Faulty' : Math.random() > 0.3 ? 'OK' : 'N/A',
        cleanBladesHousing: Math.random() > 0.6 ? 'Faulty' : Math.random() > 0.4 ? 'OK' : 'N/A',
        dustDebrisRemoval: Math.random() > 0.65 ? 'Faulty' : Math.random() > 0.35 ? 'OK' : 'N/A',
        erraticOperation: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        inletVanesOperation: Math.random() > 0.75 ? 'Faulty' : Math.random() > 0.25 ? 'OK' : 'N/A',
        bearingLubrication: Math.random() > 0.7 ? 'Faulty' : Math.random() > 0.3 ? 'OK' : 'N/A',
        noObstructionsBackflow: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        physicalDamageStability: Math.random() > 0.85 ? 'Faulty' : Math.random() > 0.15 ? 'OK' : 'N/A',
        springMountVibrationIsolator: Math.random() > 0.9 ? 'Faulty' : Math.random() > 0.1 ? 'OK' : 'N/A',
        airflowVelocity: Math.random() * 10 + 5,
        speedRpm: Math.random() * 1000 + 500,
        unusualSoundDecibel: Math.random() * 20 + 40
      },
      electricalChecks: {
        bmsControlsInterlocks: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        burntMarksDiscolorMelted: Math.random() > 0.9 ? 'Faulty' : Math.random() > 0.1 ? 'OK' : 'N/A',
        circuitBreakerFunctional: Math.random() > 0.7 ? 'Faulty' : Math.random() > 0.3 ? 'OK' : 'N/A',
        contractorsBreakers: Math.random() > 0.75 ? 'Faulty' : Math.random() > 0.25 ? 'OK' : 'N/A',
        fireAlarmConnected: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        fuseTerminals: Math.random() > 0.85 ? 'Faulty' : Math.random() > 0.15 ? 'OK' : 'N/A',
        mccPowerOffBreaker: Math.random() > 0.9 ? 'Faulty' : Math.random() > 0.1 ? 'OK' : 'N/A',
        signsLiquidLeaks: Math.random() > 0.95 ? 'Faulty' : Math.random() > 0.05 ? 'OK' : 'N/A',
        tripSettingsFunction: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        controlRelaysOperations: Math.random() > 0.75 ? 'Faulty' : Math.random() > 0.25 ? 'OK' : 'N/A',
        doorsCoversCloseProperly: Math.random() > 0.7 ? 'Faulty' : Math.random() > 0.3 ? 'OK' : 'N/A',
        frayingExposedWires: Math.random() > 0.9 ? 'Faulty' : Math.random() > 0.1 ? 'OK' : 'N/A',
        highLowSpeedVerification: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        indicationsOnOffTrip: Math.random() > 0.75 ? 'Faulty' : Math.random() > 0.25 ? 'OK' : 'N/A',
        looseWiresToBeTightened: Math.random() > 0.85 ? 'Faulty' : Math.random() > 0.15 ? 'OK' : 'N/A',
        selectorHandStopAuto: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        testEmergencyStopButton: Math.random() > 0.9 ? 'Faulty' : Math.random() > 0.1 ? 'OK' : 'N/A',
        currentAmps: Math.random() * 50 + 10,
        motorPowerKw: Math.random() * 100 + 20,
        potentialVoltage: Math.random() * 100 + 200
      },
      sequenceControlsChecks: {
        dptDifferentialPressureTransmitter: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        erraticOperationMalfunctioning: Math.random() > 0.85 ? 'Faulty' : Math.random() > 0.15 ? 'OK' : 'N/A',
        indicationsOnOffTrip: Math.random() > 0.75 ? 'Faulty' : Math.random() > 0.25 ? 'OK' : 'N/A',
        mccOffOverrideFunction: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        msfdDamperFunctional: Math.random() > 0.7 ? 'Faulty' : Math.random() > 0.3 ? 'OK' : 'N/A',
        offWithDuctDetectorActivation: Math.random() > 0.9 ? 'Faulty' : Math.random() > 0.1 ? 'OK' : 'N/A',
        overrideFscsPanelStatus: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A',
        sameTagNameInMccFan: Math.random() > 0.85 ? 'Faulty' : Math.random() > 0.15 ? 'OK' : 'N/A',
        selectorRunStopAuto: Math.random() > 0.75 ? 'Faulty' : Math.random() > 0.25 ? 'OK' : 'N/A',
        vfdVariableFrequencyDrive: Math.random() > 0.8 ? 'Faulty' : Math.random() > 0.2 ? 'OK' : 'N/A'
      },
      remarks: 'Sample inspection remarks for testing'
    };
    
    sampleData.push(checklist);
  }
  
  return sampleData;
};

export default function ChartsTestPage() {
  const sampleChecklists = generateSampleData();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            📊 Beautiful Dashboard Charts Demo
          </h1>
          <p className="text-slate-300 text-lg">
            Showcasing advanced analytics and visualizations for Auburn Engineering PPM Suite
          </p>
        </div>
        
        <DashboardAnalytics 
          checklists={sampleChecklists} 
          loading={false} 
        />
        
        <div className="mt-12 p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
          <h2 className="text-2xl font-bold text-white mb-4">📋 Chart Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-slate-300">
            <div className="space-y-2">
              <h3 className="font-semibold text-green-400">📈 Interactive Charts</h3>
              <ul className="text-sm space-y-1">
                <li>• Donut chart with center totals</li>
                <li>• Line/Area charts with gradients</li>
                <li>• Stacked/Grouped bar charts</li>
                <li>• Animated gauge charts</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-blue-400">🎨 Modern Design</h3>
              <ul className="text-sm space-y-1">
                <li>• Glass morphism effects</li>
                <li>• Smooth animations</li>
                <li>• Responsive layouts</li>
                <li>• Dark/Light theme support</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-purple-400">🔍 Smart Analytics</h3>
              <ul className="text-sm space-y-1">
                <li>• Real-time trend analysis</li>
                <li>• Failure pattern detection</li>
                <li>• Equipment health scoring</li>
                <li>• Performance insights</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 