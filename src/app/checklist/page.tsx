"use client";

import { useState, useEffect, Suspense } from "react";
import { use<PERSON><PERSON>er, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ChecklistFormData, checklistSchema } from "@/lib/validation";
import { StorageService } from "@/lib/storage";
import { ChecklistData } from "@/types/checklist";
import { EquipmentTag } from "@/types/equipment-tag";
import { 
  MECHANICAL_CHECKS, 
  ELECTRICAL_CHECKS, 
  SEQUENCE_CONTROLS_CHECKS 
} from "@/config/checklist-fields";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormField, FormItem, FormControl, FormMessage } from "@/components/ui/form";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { GeneralInfoForm } from "@/components/checklist/general-info-form";
import { ChecksSection } from "@/components/checklist/checks-section";
import { ImageUpload } from "@/components/checklist/image-upload";
import { SummaryView } from "@/components/checklist/summary-view";
import { AIRemarksGenerator } from "@/components/ai/AIRemarksGenerator";
import { Save, Eye, EyeOff, AlertTriangle, Lock } from "lucide-react";
import { AuthGuard, useAuth, ApprovalMessage } from "@/components/auth";

function ChecklistPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const editId = searchParams.get('edit');
  const { user } = useAuth();
  
  const [isSaving, setIsSaving] = useState(false);
  const [savedChecklist, setSavedChecklist] = useState<ChecklistData | null>(null);
  const [activeTab, setActiveTab] = useState("form");
  const [shouldSwitchToSummary, setShouldSwitchToSummary] = useState(false);
  const [selectedEquipmentTag, setSelectedEquipmentTag] = useState<EquipmentTag | null>(null);

  const form = useForm<ChecklistFormData>({
    resolver: zodResolver(checklistSchema),
    defaultValues: {
      generalInfo: {
        clientName: "",
        building: "",
        inspectedBy: "",
        approvedBy: "",
        date: new Date().toISOString().split('T')[0],
        ppmAttempt: 1,
        equipmentName: "",
        location: "",
        tagNo: "",
      },
      mechanicalChecks: {
        beltWearPulleyAlignment: "OK",
        bladeImpellerDamage: "OK",
        boltSetScrewTightness: "OK",
        bladeTipClearance: "OK",
        excessiveVibration: "OK",
        fanGuardProtection: "OK",
        fanPowerOff: "OK",
        motorOverheating: "OK",
        rotationDirection: "OK",
        cleanBladesHousing: "OK",
        dustDebrisRemoval: "OK",
        erraticOperation: "OK",
        inletVanesOperation: "OK",
        bearingLubrication: "OK",
        noObstructionsBackflow: "OK",
        physicalDamageStability: "OK",
        springMountVibrationIsolator: "OK",
      },
      electricalChecks: {
        bmsControlsInterlocks: "OK",
        burntMarksDiscolorMelted: "OK",
        circuitBreakerFunctional: "OK",
        contractorsBreakers: "OK",
        fireAlarmConnected: "OK",
        fuseTerminals: "OK",
        mccPowerOffBreaker: "OK",
        signsLiquidLeaks: "OK",
        tripSettingsFunction: "OK",
        controlRelaysOperations: "OK",
        doorsCoversCloseProperly: "OK",
        frayingExposedWires: "OK",
        highLowSpeedVerification: "OK",
        indicationsOnOffTrip: "OK",
        looseWiresToBeTightened: "OK",
        selectorHandStopAuto: "OK",
        testEmergencyStopButton: "OK",
      },
      sequenceControlsChecks: {
        dptDifferentialPressureTransmitter: "OK",
        erraticOperationMalfunctioning: "OK",
        indicationsOnOffTrip: "OK",
        mccOffOverrideFunction: "OK",
        msfdDamperFunctional: "OK",
        offWithDuctDetectorActivation: "OK",
        overrideFscsPanelStatus: "OK",
        sameTagNameInMccFan: "OK",
        selectorRunStopAuto: "OK",
        vfdVariableFrequencyDrive: "OK",
      },
      remarks: "",
    },
  });

  // Switch to summary tab when savedChecklist is set and flag is true
  useEffect(() => {
    if (savedChecklist && shouldSwitchToSummary) {
      setActiveTab("summary");
      setShouldSwitchToSummary(false);
    }
  }, [savedChecklist, shouldSwitchToSummary]);

  // Load existing checklist if editing
  useEffect(() => {
    if (editId) {
      const existingChecklist = StorageService.getChecklistById(editId);
      if (existingChecklist) {
        // Check if checklist is completed and user is not admin
        if (existingChecklist.isCompleted) {
          alert("This checklist has been marked as completed and can only be modified by administrators.");
          router.push('/saved');
          return;
        }

        // Helper function to convert string numbers to actual numbers
        const convertToNumber = (value: number | string | undefined): number | undefined => {
          if (typeof value === 'string' && value !== '') {
            const num = Number(value);
            return isNaN(num) ? undefined : num;
          }
          return typeof value === 'number' ? value : undefined;
        };

        // Ensure ppmAttempt is a number (in case of old data with string values)
        const generalInfo = {
          ...existingChecklist.generalInfo,
          ppmAttempt: typeof existingChecklist.generalInfo.ppmAttempt === 'string' 
            ? Number(existingChecklist.generalInfo.ppmAttempt) 
            : existingChecklist.generalInfo.ppmAttempt
        };

        // Convert number fields in mechanical checks
        const mechanicalChecks = {
          ...existingChecklist.mechanicalChecks,
          airflowVelocity: convertToNumber(existingChecklist.mechanicalChecks.airflowVelocity),
          speedRpm: convertToNumber(existingChecklist.mechanicalChecks.speedRpm),
          unusualSoundDecibel: convertToNumber(existingChecklist.mechanicalChecks.unusualSoundDecibel),
        };

        // Convert number fields in electrical checks
        const electricalChecks = {
          ...existingChecklist.electricalChecks,
          currentAmps: convertToNumber(existingChecklist.electricalChecks.currentAmps),
          motorPowerKw: convertToNumber(existingChecklist.electricalChecks.motorPowerKw),
          potentialVoltage: convertToNumber(existingChecklist.electricalChecks.potentialVoltage),
        };

        form.reset({
          generalInfo,
          mechanicalChecks,
          electricalChecks,
          sequenceControlsChecks: existingChecklist.sequenceControlsChecks,
          remarks: existingChecklist.remarks,
          beforeImage: existingChecklist.beforeImage,
          afterImage: existingChecklist.afterImage,
          inspectorSignature: existingChecklist.inspectorSignature,
        });

        // For editing mode, if we have equipment data, create a mock equipment tag
        // This is a fallback for existing checklists that don't have equipment tag data
        if (generalInfo.tagNo && generalInfo.clientName && generalInfo.equipmentName) {
          const mockEquipmentTag: EquipmentTag = {
            id: 'legacy-' + generalInfo.tagNo,
            contractor: 'Auburn Engineering WLL',
            clientName: generalInfo.clientName,
            equipmentName: generalInfo.equipmentName,
            tagNumber: generalInfo.tagNo,
            dateOfCreation: existingChecklist.createdAt?.split('T')[0] || new Date().toISOString().split('T')[0],
            building: generalInfo.building,
            location: generalInfo.location,
            qrCodeData: '',
            createdAt: existingChecklist.createdAt || new Date().toISOString(),
            updatedAt: existingChecklist.updatedAt || new Date().toISOString(),
            createdBy: existingChecklist.userId || user?.uid || '',
          };
          setSelectedEquipmentTag(mockEquipmentTag);
        }
      }
    }
  }, [editId, router, user]);

  const handleEquipmentTagSelect = (tag: EquipmentTag) => {
    setSelectedEquipmentTag(tag);
  };

  const handleEquipmentTagClear = () => {
    setSelectedEquipmentTag(null);
  };

  const onSubmit = async (data: ChecklistFormData) => {
    if (!user) {
      alert("You must be signed in to save checklists.");
      return;
    }

    // Validate that equipment tag is selected
    if (!selectedEquipmentTag) {
      alert("Please select a valid equipment tag before saving the checklist.");
      return;
    }

    setIsSaving(true);
    try {
      const checklistData: ChecklistData = {
        id: editId || StorageService.generateId(),
        createdAt: editId ? 
          StorageService.getChecklistById(editId)?.createdAt || new Date().toISOString() : 
          new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: user.uid, // Associate with authenticated user
        syncMetadata: editId ? 
          StorageService.getChecklistById(editId)?.syncMetadata || StorageService.createDefaultSyncMetadata() :
          StorageService.createDefaultSyncMetadata(),
        equipmentTagId: selectedEquipmentTag.id, // Store equipment tag reference
        isCompleted: editId ? 
          StorageService.getChecklistById(editId)?.isCompleted || false :
          false, // New checklists start as not completed
        ...data,
      };

      const success = await StorageService.saveChecklist(checklistData);
      if (success) {
        setSavedChecklist(checklistData);
        setShouldSwitchToSummary(true);
        if (!editId) {
          // Update URL to show we're now editing this checklist
          router.replace(`/checklist?edit=${checklistData.id}`);
        }
      } else {
        alert("Failed to save checklist. Storage quota may be exceeded. Please try syncing data to cloud or clearing old checklists.");
      }
    } catch (error) {
      console.error("Error saving checklist:", error);
      alert("Failed to save checklist. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // Check if inspection sections should be blocked
  const isInspectionBlocked = !selectedEquipmentTag;

  return (
    <div className="max-w-6xl mx-auto space-y-6 p-4">
      {/* Approval Message for Users */}
      <ApprovalMessage />
      
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">
            {editId ? "Edit Inspection Checklist" : "New Inspection Checklist"}
          </h1>
          <p className="text-muted-foreground">
            Complete the inspection checklist for ACMV systems
            {selectedEquipmentTag && (
              <span className="block mt-1 text-green-600 font-medium">
                Equipment: {selectedEquipmentTag.tagNumber} - {selectedEquipmentTag.equipmentName}
              </span>
            )}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            onClick={() => setActiveTab(activeTab === "form" ? "summary" : "form")}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            {activeTab === "form" ? (
              <>
                <Eye className="h-4 w-4" />
                Preview
              </>
            ) : (
              <>
                <EyeOff className="h-4 w-4" />
                Edit
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="form" className="flex items-center gap-2">
            <Save className="h-4 w-4" />
            Form
          </TabsTrigger>
          <TabsTrigger value="summary" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Summary
          </TabsTrigger>
        </TabsList>

        <TabsContent value="form" className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* General Information with Equipment Tag Selection */}
              <GeneralInfoForm 
                form={form} 
                selectedEquipmentTag={selectedEquipmentTag}
                onEquipmentTagSelect={handleEquipmentTagSelect}
                onEquipmentTagClear={handleEquipmentTagClear}
              />

              {/* Blocked Access Warning */}
              {isInspectionBlocked && (
                <Alert variant="destructive">
                  <Lock className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Inspection sections locked:</strong> Please select a valid equipment tag above to access the inspection checklist sections.
                  </AlertDescription>
                </Alert>
              )}

              {/* Mechanical Checks */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <ChecksSection
                  title="Mechanical Checks"
                  description="Physical inspection of mechanical components"
                  fields={MECHANICAL_CHECKS}
                  section="mechanical"
                  form={form}
                  defaultOpen={!isInspectionBlocked}
                />
              </div>

              {/* Electrical Checks */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <ChecksSection
                  title="Electrical Checks"
                  description="Electrical system and component inspection"
                  fields={ELECTRICAL_CHECKS}
                  section="electrical"
                  form={form}
                />
              </div>

              {/* Sequence Controls Checks */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <ChecksSection
                  title="Sequence Controls Checks"
                  description="Control system and sequence verification"
                  fields={SEQUENCE_CONTROLS_CHECKS}
                  section="sequence"
                  form={form}
                />
              </div>

              {/* Remarks */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <AIRemarksGenerator
                  checklistData={form.getValues()}
                  currentRemarks={form.watch("remarks")}
                  onRemarksChange={(remarks) => form.setValue("remarks", remarks)}
                  disabled={isInspectionBlocked}
                  placeholder="Enter any additional remarks, observations, or recommendations..."
                />
              </div>

              {/* Image Uploads and Signature */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <ImageUpload form={form} />
              </div>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button 
                  type="submit" 
                  disabled={isSaving || isInspectionBlocked}
                  size="lg"
                  className="w-full sm:w-auto"
                >
                  {isSaving ? (
                    <>
                      <Save className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {editId ? "Update Checklist" : "Save Checklist"}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>

        <TabsContent value="summary" className="space-y-6">
          {savedChecklist && (
            <SummaryView checklist={savedChecklist} />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function ChecklistPage() {
  return (
    <AuthGuard>
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      }>
        <ChecklistPageContent />
      </Suspense>
    </AuthGuard>
  );
} 