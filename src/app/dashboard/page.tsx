'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { AuthGuard, useAuth, ApprovalMessage } from '@/components/auth';
import { User, CheckCircle, BarChart3, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { GradientText } from '@/components/gradient-text';
import { StorageService } from '@/lib/storage-adapter';
import { ChecklistData } from '@/types/checklist';
import { ErrorBoundary } from '@/components/error-boundary';
import { DashboardAnalytics } from '@/components/dashboard/DashboardAnalytics';
import { UserDebug } from '@/components/debug/user-debug';

export default function DashboardPage() {
  return (
    <AuthGuard>
      <ErrorBoundary>
        <DashboardContent />
      </ErrorBoundary>
    </AuthGuard>
  );
}

function DashboardContent() {
  const { user, extendedUser, loading: authLoading } = useAuth();
  const [checklists, setChecklists] = useState<ChecklistData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadChecklists = () => {
      try {
        const saved = StorageService.getAllChecklists();
        setChecklists(saved);
      } catch (error) {
        console.error('Error loading checklists:', error);
      } finally {
        setLoading(false);
      }
    };

    loadChecklists();

    // Listen for storage changes
    const handleStorageChange = () => {
      loadChecklists();
    };

    window.addEventListener('storage', handleStorageChange);
    // Custom event for same-tab updates
    window.addEventListener('checklistsUpdated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('checklistsUpdated', handleStorageChange);
    };
  }, []);

  // Calculate dynamic metrics with proper error handling
  const calculateMetrics = () => {
    const totalChecklists = checklists.length;
    const completedInspections = checklists.filter(c => c.syncMetadata.status === 'synced').length;
    
    // Calculate profile completion percentage
    const calculateProfileCompletion = () => {
      if (!user) return 0;
      
      let completedFields = 0;
      let totalFields = 4; // Basic fields: email, displayName, emailVerified, photoURL
      
      // Basic Firebase Auth fields
      if (user.email) completedFields++;
      if (user.displayName) completedFields++;
      if (user.emailVerified) completedFields++;
      if (user.photoURL) completedFields++;
      
      // Extended user fields (if available)
      if (extendedUser) {
        totalFields += 2; // role and isActive
        if (extendedUser.role) completedFields++;
        if (extendedUser.isActive !== undefined) completedFields++;
      }
      
      return Math.round((completedFields / totalFields) * 100);
    };

    return {
      totalChecklists,
      completedInspections,
      profileCompletion: calculateProfileCompletion()
    };
  };

  const metrics = calculateMetrics();

  // Calculate success rate with proper handling of edge cases
  const calculateSuccessRate = () => {
    if (checklists.length === 0) {
      return { rate: 0, display: 'No data' };
    }
    
    const syncedCount = checklists.filter(c => c.syncMetadata.status === 'synced').length;
    const rate = Math.round((syncedCount / checklists.length) * 100);
    
    return { rate, display: `${rate}%` };
  };

  const successRate = calculateSuccessRate();

  const stats = [
    {
      title: 'Completed Inspections',
      value: loading ? '...' : metrics.completedInspections.toString(),
      icon: CheckCircle,
      color: 'text-green-500',
      bgColor: 'bg-green-500/10',
    },
    {
      title: 'Total Checklists',
      value: loading ? '...' : metrics.totalChecklists.toString(),
      icon: BarChart3,
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10',
    },
    {
      title: 'Profile Completion',
      value: loading ? '...' : `${metrics.profileCompletion}%`,
      icon: User,
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10',
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Approval Message for Users */}
      <ApprovalMessage />

      {/* Debug Information (temporary) */}
      <UserDebug />

      {/* Welcome Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">
          <GradientText gradient="from-blue-400 to-teal-400">
            Welcome back, {user?.displayName || user?.email?.split('@')[0]}!
          </GradientText>
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          This is your personalized dashboard. Here you can view your inspection history, 
          manage your profile, and access advanced features.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title} className="p-6 glass border-border/50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                  <p className="text-3xl font-bold mt-2">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Advanced Analytics Section */}
      <DashboardAnalytics checklists={checklists} loading={loading} />

      {/* Detailed Statistics - Keep for backwards compatibility but make it more compact */}
      {!loading && checklists.length > 0 && (
        <Card className="p-6 glass border-border/50">
          <h3 className="text-lg font-semibold mb-4">Quick Status Overview</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {checklists.filter(c => c.syncMetadata.status === 'synced').length}
              </div>
              <p className="text-xs text-muted-foreground">Synced</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {checklists.filter(c => c.syncMetadata.status === 'local-only').length}
              </div>
              <p className="text-xs text-muted-foreground">Local Only</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {checklists.filter(c => c.syncMetadata.status === 'pending').length}
              </div>
              <p className="text-xs text-muted-foreground">Pending Sync</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {successRate.display}
              </div>
              <p className="text-xs text-muted-foreground">Success Rate</p>
            </div>
          </div>
        </Card>
      )}

      {/* Show message when no checklists exist */}
      {!loading && checklists.length === 0 && (
        <Card className="p-6 glass border-border/50 text-center">
          <h3 className="text-lg font-semibold mb-2">No Inspections Yet</h3>
          <p className="text-muted-foreground mb-4">
            Start your first inspection to see your dashboard statistics.
          </p>
          <Button asChild>
            <a href="/checklist">
              <CheckCircle className="h-4 w-4 mr-2" />
              Start New Inspection
            </a>
          </Button>
        </Card>
      )}

      {/* User Profile Card */}
      <Card className="p-6 glass border-border/50">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:justify-between">
          <div className="flex items-center space-x-4 flex-1 min-w-0">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full flex items-center justify-center text-white text-xl font-bold flex-shrink-0">
              {user?.photoURL ? (
                <img 
                  src={user.photoURL} 
                  alt="Profile" 
                  className="w-16 h-16 rounded-full object-cover"
                />
              ) : (
                user?.displayName?.[0] || user?.email?.[0] || 'U'
              )}
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="text-xl font-semibold truncate">{user?.displayName || 'User'}</h3>
              <p className="text-muted-foreground truncate">{user?.email}</p>
              <div className="flex items-center mt-2">
                <div className={`w-2 h-2 rounded-full mr-2 ${user?.emailVerified ? 'bg-green-500' : 'bg-yellow-500'}`} />
                <span className="text-sm text-muted-foreground">
                  {user?.emailVerified ? 'Email Verified' : 'Email Not Verified'}
                </span>
              </div>
            </div>
          </div>
          <Button variant="outline" className="flex items-center space-x-2 w-full sm:w-auto flex-shrink-0">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Edit Profile</span>
            <span className="sm:hidden">Edit</span>
          </Button>
        </div>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6 glass border-border/50">
          <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <Button className="w-full justify-start" variant="outline" asChild>
              <a href="/checklist">
                <CheckCircle className="h-4 w-4 mr-2" />
                Start New Inspection
              </a>
            </Button>
            <Button className="w-full justify-start" variant="outline" asChild>
              <a href="/saved">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Saved Inspections
              </a>
            </Button>
          </div>
        </Card>

        <Card className="p-6 glass border-border/50">
          <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
          <div className="space-y-3 text-sm">
            {loading ? (
              <div className="text-muted-foreground">Loading...</div>
            ) : checklists.length > 0 ? (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Latest inspection</span>
                  <span>
                    {(() => {
                      const latest = checklists.sort((a, b) => 
                        new Date(b.updatedAt || b.createdAt).getTime() - new Date(a.updatedAt || a.createdAt).getTime()
                      )[0];
                      const date = new Date(latest.updatedAt || latest.createdAt);
                      const now = new Date();
                      const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
                      
                      if (diffInHours < 1) return 'Just now';
                      if (diffInHours < 24) return `${diffInHours}h ago`;
                      const diffInDays = Math.floor(diffInHours / 24);
                      return `${diffInDays}d ago`;
                    })()}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Total this week</span>
                  <span>
                    {(() => {
                      const oneWeekAgo = new Date();
                      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
                      return checklists.filter(c => 
                        new Date(c.createdAt) > oneWeekAgo
                      ).length;
                    })()}
                  </span>
                </div>
              </>
            ) : (
              <div className="text-muted-foreground">No inspections yet</div>
            )}
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Account created</span>
              <span>
                {extendedUser?.createdAt ? 
                  (() => {
                    const created = new Date(extendedUser.createdAt);
                    const now = new Date();
                    const diffInDays = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
                    if (diffInDays === 0) return 'Today';
                    if (diffInDays === 1) return 'Yesterday';
                    if (diffInDays < 30) return `${diffInDays} days ago`;
                    const diffInMonths = Math.floor(diffInDays / 30);
                    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
                  })() 
                  : 'Recently'
                }
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
} 