@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif;
  --font-inter: 'Inter', ui-sans-serif, system-ui, sans-serif;
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: rgb(4 12 20);
  --foreground: rgb(241 245 249);
  --card: rgb(15 26 40);
  --card-foreground: rgb(241 245 249);
  --popover: rgb(15 26 40);
  --popover-foreground: rgb(241 245 249);
  --primary: rgb(0 124 240);
  --primary-foreground: rgb(241 245 249);
  --secondary: rgb(30 52 75);
  --secondary-foreground: rgb(186 207 230);
  --muted: rgb(30 52 75);
  --muted-foreground: rgb(148 177 205);
  --accent: rgb(0 201 167);
  --accent-foreground: rgb(4 12 20);
  --destructive: rgb(239 68 68);
  --border: rgb(51 85 120);
  --input: rgb(51 85 120);
  --ring: rgb(0 124 240);
  --chart-1: rgb(0 124 240);
  --chart-2: rgb(0 201 167);
  --chart-3: rgb(34 197 255);
  --chart-4: rgb(16 185 129);
  --chart-5: rgb(6 182 212);
  --sidebar: rgb(15 26 40);
  --sidebar-foreground: rgb(241 245 249);
  --sidebar-primary: rgb(0 124 240);
  --sidebar-primary-foreground: rgb(241 245 249);
  --sidebar-accent: rgb(30 52 75);
  --sidebar-accent-foreground: rgb(186 207 230);
  --sidebar-border: rgb(51 85 120);
  --sidebar-ring: rgb(0 124 240);
}

.dark {
  --background: rgb(4 12 20);
  --foreground: rgb(241 245 249);
  --card: rgb(15 26 40);
  --card-foreground: rgb(241 245 249);
  --popover: rgb(15 26 40);
  --popover-foreground: rgb(241 245 249);
  --primary: rgb(0 124 240);
  --primary-foreground: rgb(241 245 249);
  --secondary: rgb(30 52 75);
  --secondary-foreground: rgb(186 207 230);
  --muted: rgb(30 52 75);
  --muted-foreground: rgb(148 177 205);
  --accent: rgb(0 201 167);
  --accent-foreground: rgb(4 12 20);
  --destructive: rgb(239 68 68);
  --border: rgb(51 85 120);
  --input: rgb(51 85 120);
  --ring: rgb(0 124 240);
  --chart-1: rgb(0 124 240);
  --chart-2: rgb(0 201 167);
  --chart-3: rgb(34 197 255);
  --chart-4: rgb(16 185 129);
  --chart-5: rgb(6 182 212);
  --sidebar: rgb(15 26 40);
  --sidebar-foreground: rgb(241 245 249);
  --sidebar-primary: rgb(0 124 240);
  --sidebar-primary-foreground: rgb(241 245 249);
  --sidebar-accent: rgb(30 52 75);
  --sidebar-accent-foreground: rgb(186 207 230);
  --sidebar-border: rgb(51 85 120);
  --sidebar-ring: rgb(0 124 240);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    background: radial-gradient(ellipse at top, rgb(15 26 40) 0%, rgb(4 12 20) 50%);
    min-height: 100vh;
  }
}

/* Custom Gradient Animations */
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gradient-y {
  0%, 100% {
    background-position: 50% 0%;
  }
  50% {
    background-position: 50% 100%;
  }
}

@keyframes gradient-xy {
  0%, 100% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
}

.animate-gradient-y {
  animation: gradient-y 15s ease infinite;
}

.animate-gradient-xy {
  animation: gradient-xy 15s ease infinite;
}

/* Custom Glass Effect */
.glass {
  background: rgba(15, 26, 40, 0.6);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(0, 124, 240, 0.1);
}

/* Custom Grid Pattern */
.bg-grid-pattern {
  background-image: radial-gradient(circle, rgba(0, 124, 240, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Pulse Glow */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 124, 240, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 201, 167, 0.4);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}
