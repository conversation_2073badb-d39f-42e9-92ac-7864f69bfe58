import type { Metadata, Viewport } from "next";
// Remove the problematic next/font/google import
// import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { AuthProvider } from "@/components/auth";
import { StorageAlert } from "@/components/storage/storage-alert";
import { AppInitialization } from "@/components/app-initialization";

// Remove the Inter font variable since we'll load it via CSS
// const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Auburn Suite",
  description: "Auburn Engineering's comprehensive PPM Suite for ACMV system inspections with QCD compliance and offline support",
  manifest: "/manifest.json",
  icons: {
    icon: [
      {
        url: "/favicon.svg",
        type: "image/svg+xml",
      },
      {
        url: "/favicon.ico",
        type: "image/x-icon",
      },
    ],
    apple: "/apple-touch-icon.png",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#020617",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="font-inter">
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <AppInitialization>
              <div className="min-h-screen relative overflow-hidden flex flex-col">
                <Navigation />
                <main className="container mx-auto max-w-7xl relative z-10 flex-1">
                  <div className="relative">
                    <div className="absolute inset-0 bg-grid-pattern opacity-[0.03] pointer-events-none" />
                    {children}
                  </div>
                </main>
                <Footer />
                <StorageAlert />
              </div>
            </AppInitialization>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
