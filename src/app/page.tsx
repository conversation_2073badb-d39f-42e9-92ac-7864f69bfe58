'use client'

import Link from "next/link";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckSquare, Database, Download, FileText, Smartphone, Wifi, Shield, Zap, Clock, Settings, Brain, Users, BarChart3, Cloud } from "lucide-react";
import ThreeBackground from "@/components/three-background";
import GradientText from "@/components/gradient-text";
import { AnimatedFeatureCard } from "@/components/animated-card";

export default function HomePage() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <div className="p-4 lg:p-6 pb-20 lg:pb-32">
      <ThreeBackground />
      <motion.div 
        className="space-y-20 relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Hero Section */}
        <motion.div 
          className="text-center space-y-8 pt-12"
          variants={itemVariants}
        >
          <motion.div
            className="space-y-4"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black tracking-tight">
              <GradientText 
                gradient="from-slate-200 via-blue-200 to-cyan-200"
                className="block"
              >
                Auburn
              </GradientText>
              <GradientText 
                gradient="from-[#007CF0] via-[#0099DD] to-[#00C9A7]"
                className="block"
              >
                PPM Suite
              </GradientText>
            </h1>
            <motion.div
              className="h-1 w-32 mx-auto bg-gradient-to-r from-[#007CF0] to-[#00C9A7] rounded-full"
              initial={{ width: 0 }}
              animate={{ width: 128 }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </motion.div>

          <motion.p 
            className="text-lg sm:text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed px-4"
            variants={itemVariants}
          >
            Advanced digital inspection suite with AI-powered insights, cloud synchronization, and comprehensive user management. 
            Streamline Auburn Engineering&apos;s PPM processes with QCD-compliant solutions and real-time analytics.
          </motion.p>

          <motion.div 
            className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center pt-8 px-4"
            variants={itemVariants}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full sm:w-auto"
            >
              <Button 
                asChild 
                size="lg" 
                className="w-full sm:w-auto rounded-2xl px-6 sm:px-8 py-6 text-lg font-semibold bg-gradient-to-r from-[#007CF0] to-[#00C9A7] hover:from-[#0066CC] hover:to-[#00B394] text-white shadow-2xl shadow-blue-500/25 hover:shadow-teal-500/40 transition-all duration-300 border-0"
              >
                <Link href="/checklist">
                  <CheckSquare className="mr-3 h-6 w-6" />
                  Start New Inspection
                </Link>
              </Button>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full sm:w-auto"
            >
              <Button 
                asChild 
                variant="outline" 
                size="lg" 
                className="w-full sm:w-auto rounded-2xl px-6 sm:px-8 py-6 text-lg font-semibold glass border-blue-500/30 hover:bg-blue-900/20 transition-all duration-300 text-slate-200"
              >
                <Link href="/dashboard">
                  <BarChart3 className="mr-3 h-6 w-6" />
                  View Dashboard
                </Link>
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Enhanced Features Grid */}
        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 px-4"
          variants={containerVariants}
        >
          <AnimatedFeatureCard
            icon={<Brain className="h-6 w-6" />}
            title="AI-Powered Insights"
            description="Advanced AI integration with Gemini 2.0 Flash for intelligent remark generation, analysis, and professional inspection reports with multiple tone options."
            delay={0.1}
          />

          <AnimatedFeatureCard
            icon={<Cloud className="h-6 w-6" />}
            title="Cloud Synchronization"
            description="Seamless Firebase integration with real-time sync, conflict resolution, automatic backups, and secure data management across all devices."
            delay={0.2}
          />

          <AnimatedFeatureCard
            icon={<Users className="h-6 w-6" />}
            title="User Management"
            description="Complete authentication system with role-based access control, admin panel for user management, and comprehensive security features."
            delay={0.3}
          />

          <AnimatedFeatureCard
            icon={<BarChart3 className="h-6 w-6" />}
            title="Dynamic Dashboard"
            description="Real-time analytics with live metrics calculation, profile completion tracking, inspection overview, and activity monitoring."
            delay={0.4}
          />

          <AnimatedFeatureCard
            icon={<FileText className="h-6 w-6" />}
            title="QCD Compliant Checklist"
            description="Complete digital version of JF 10 checklist with 50 inspection points covering mechanical, electrical, and sequence controls per Qatar Civil Defense standards."
            delay={0.5}
          />

          <AnimatedFeatureCard
            icon={<Wifi className="h-6 w-6" />}
            title="Offline-First Design"
            description="Works completely offline with automatic sync when online. Perfect for basement inspections and remote locations with intelligent conflict resolution."
            delay={0.6}
          />

          <AnimatedFeatureCard
            icon={<Download className="h-6 w-6" />}
            title="Advanced Export"
            description="Export inspection data as JSON or CSV files with enhanced validation, automatic fallback mechanisms, and comprehensive reporting options."
            delay={0.7}
          />

          <AnimatedFeatureCard
            icon={<Shield className="h-6 w-6" />}
            title="Enhanced Security"
            description="Role-based access control, audit logging, secure data encryption, and comprehensive authorization checks for all sensitive operations."
            delay={0.8}
          />

          <AnimatedFeatureCard
            icon={<Smartphone className="h-6 w-6" />}
            title="Mobile Optimized"
            description="Responsive design with PWA capabilities, optimized for tablets and smartphones used on-site across hospitality, hospitals, and commercial projects."
            delay={0.9}
          />
        </motion.div>

        {/* Enhanced Stats Section */}
        <motion.div 
          className="glass rounded-3xl p-6 sm:p-8 md:p-12 border border-blue-500/20 mx-4"
          variants={itemVariants}
        >
          <motion.h2 
            className="text-3xl sm:text-4xl font-bold mb-8 text-center"
            variants={itemVariants}
          >
            <GradientText gradient="from-blue-200 to-cyan-200">
              Comprehensive Inspection Coverage
            </GradientText>
          </motion.h2>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
            {[
              { number: "20", label: "Mechanical Checks", delay: 0.1 },
              { number: "20", label: "Electrical Checks", delay: 0.2 },
              { number: "10", label: "Sequence Controls", delay: 0.3 },
              { number: "50", label: "Total QCD Points", delay: 0.4 }
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center group"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: stat.delay }}
                whileHover={{ scale: 1.05 }}
              >
                <motion.div 
                  className="text-4xl sm:text-5xl md:text-6xl font-black mb-2"
                  whileHover={{ textShadow: "0 0 20px rgba(0, 124, 240, 0.5)" }}
                >
                  <GradientText 
                    gradient="from-[#007CF0] to-[#00C9A7]"
                    animate={false}
                  >
                    {stat.number}
                  </GradientText>
                </motion.div>
                <div className="text-slate-400 text-sm sm:text-base lg:text-lg font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Additional Features Section */}
        <motion.div 
          className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 px-4"
          variants={containerVariants}
        >
          <motion.div
            className="glass rounded-3xl p-6 sm:p-8 hover:shadow-2xl hover:shadow-blue-500/20 transition-all duration-300 border border-blue-500/20"
            variants={itemVariants}
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center gap-4 mb-6">
              <motion.div
                className="p-3 rounded-2xl bg-gradient-to-r from-blue-600 to-blue-700"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
              >
                <Clock className="h-6 sm:h-8 w-6 sm:w-8 text-white" />
              </motion.div>
              <h3 className="text-xl sm:text-2xl font-bold">
                <GradientText gradient="from-blue-200 to-cyan-200">
                  Real-time Analytics
                </GradientText>
              </h3>
            </div>
            <p className="text-slate-300 text-base sm:text-lg leading-relaxed">
              Dynamic dashboard with live metrics calculation, inspection progress tracking, completion rates, 
              and comprehensive activity monitoring with detailed insights.
            </p>
          </motion.div>

          <motion.div
            className="glass rounded-3xl p-6 sm:p-8 hover:shadow-2xl hover:shadow-teal-500/20 transition-all duration-300 border border-blue-500/20"
            variants={itemVariants}
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center gap-4 mb-6">
              <motion.div
                className="p-3 rounded-2xl bg-gradient-to-r from-teal-600 to-teal-700"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
              >
                <Settings className="h-6 sm:h-8 w-6 sm:w-8 text-white" />
              </motion.div>
              <h3 className="text-xl sm:text-2xl font-bold">
                <GradientText gradient="from-blue-200 to-cyan-200">
                  Advanced Administration
                </GradientText>
              </h3>
            </div>
            <p className="text-slate-300 text-base sm:text-lg leading-relaxed">
              Comprehensive admin panel with user management, role-based permissions, audit logging, 
              and advanced controls for team collaboration and oversight.
            </p>
          </motion.div>
        </motion.div>

        {/* Technology Stack Section */}
        <motion.div 
          className="glass rounded-3xl p-6 sm:p-8 md:p-12 border border-blue-500/20 mx-4"
          variants={itemVariants}
        >
          <motion.h2 
            className="text-3xl sm:text-4xl font-bold mb-8 text-center"
            variants={itemVariants}
          >
            <GradientText gradient="from-blue-200 to-cyan-200">
              Powered by Modern Technology
            </GradientText>
          </motion.h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            {[
              { name: "Next.js 15", description: "React Framework" },
              { name: "Firebase", description: "Backend & Auth" },
              { name: "AI Integration", description: "Gemini 2.0 Flash" },
              { name: "TypeScript", description: "Type Safety" },
            ].map((tech, index) => (
              <motion.div
                key={index}
                className="p-4 rounded-xl border border-blue-500/20 hover:border-blue-500/40 transition-colors duration-300"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 + index * 0.1 }}
                whileHover={{ scale: 1.02 }}
              >
                <h3 className="font-semibold text-white mb-1">{tech.name}</h3>
                <p className="text-slate-400 text-sm">{tech.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
