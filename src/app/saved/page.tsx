"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { calculateChecklistSummary } from "@/lib";
import { StorageService } from "@/lib/storage-adapter";
import { ChecklistData } from "@/types/checklist";
import { exportToExcel, exportToPDF } from "@/lib/export";
import { AuthGuard, useAuth, ApprovalMessage } from "@/components/auth";
import { StorageManager } from "@/components/storage/storage-manager";
import { SyncStatus } from "@/components/sync/sync-status";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Edit,
  Trash2,
  FileSpreadsheet,
  FileText,
  Plus,
  Database,
  Loader2,
  Building,
  MapPin,
  Calendar,
  User,
  Tag,
  Cloud,
  CloudOff,
  Upload,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Lock,
  X,
} from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";

function SavedInspectionsPageContent() {
  const router = useRouter();
  const { user } = useAuth();
  const [checklists, setChecklists] = useState<ChecklistData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [exportingStates, setExportingStates] = useState<Record<string, { pdf: boolean; excel: boolean }>>({});
  const [uploadingStates, setUploadingStates] = useState<Record<string, boolean>>({});
  
  // Warning dialog state for upload confirmation
  const [uploadWarningDialog, setUploadWarningDialog] = useState<{
    isOpen: boolean;
    checklist: ChecklistData | null;
  }>({
    isOpen: false,
    checklist: null,
  });

  useEffect(() => {
    loadChecklists();
    
    // Note: Auto-sync is now handled by Firestore persistence automatically
    // CloudStorageService is deprecated in favor of Firestore real-time sync
  }, [user]);

  // Listen for checklist updates
  useEffect(() => {
    const handleChecklistsUpdated = () => {
      loadChecklists();
    };

    window.addEventListener('checklistsUpdated', handleChecklistsUpdated);
    
    return () => {
      window.removeEventListener('checklistsUpdated', handleChecklistsUpdated);
    };
  }, []);

  const loadChecklists = async () => {
    setIsLoading(true);
    try {
    if (user) {
      // Only show checklists for the authenticated user
        const allChecklists = await StorageService.getAllChecklistsAsync();
        const userChecklists = allChecklists.filter(c => c.userId === user.uid);
      setChecklists(userChecklists);
    } else {
        setChecklists([]);
      }
    } catch (error) {
      console.error('Failed to load checklists:', error);
      setChecklists([]);
    }
    setIsLoading(false);
  };

  const handleDelete = async (id: string) => {
    const success = await StorageService.deleteChecklist(id);
    if (success) {
      loadChecklists();
    } else {
      alert("Failed to delete checklist. Please try again.");
    }
  };

  const handleClearAll = async () => {
    try {
      const success = await StorageService.clearAllChecklists();
    if (success) {
      loadChecklists();
    } else {
        alert("Failed to clear all checklists. Please try again.");
      }
    } catch (error) {
      console.error('Failed to clear checklists:', error);
      alert("Failed to clear all checklists. Please try again.");
    }
  };

  const handleExport = async (checklist: ChecklistData, type: 'excel' | 'pdf') => {
    try {
      setExportingStates(prev => ({
        ...prev,
        [checklist.id]: {
          ...prev[checklist.id],
          [type]: true
        }
      }));

      if (type === 'excel') {
        await exportToExcel(checklist);
      } else {
        await exportToPDF(checklist);
      }
    } catch (error) {
      console.error(`Export ${type} failed:`, error);
      alert(`Failed to export ${type.toUpperCase()} file. Please try again.`);
    } finally {
      setExportingStates(prev => ({
        ...prev,
        [checklist.id]: {
          ...prev[checklist.id],
          [type]: false
        }
      }));
    }
  };

  const handleUploadToCloud = async (checklist: ChecklistData) => {
    if (!user) return;

    // Show warning dialog before upload
    setUploadWarningDialog({
      isOpen: true,
      checklist: checklist,
    });
  };

  const confirmUploadToCloud = async () => {
    const { checklist } = uploadWarningDialog;
    if (!user || !checklist) return;

    try {
      setUploadingStates(prev => ({ ...prev, [checklist.id]: true }));
      
      // Mark as completed and update sync status
      const updatedChecklist = {
        ...checklist,
        isCompleted: true,
        completedAt: new Date().toISOString(),
        syncMetadata: {
          ...checklist.syncMetadata,
          status: 'synced' as const,
          lastSyncTime: new Date().toISOString()
        }
      };

      const success = await StorageService.saveChecklist(updatedChecklist);
      if (success) {
        loadChecklists(); // Refresh to show updated sync status
        alert("Checklist uploaded successfully! It is now marked as complete and can only be modified by administrators.");
      } else {
        alert("Failed to upload to cloud. Please try again.");
      }
    } catch (error) {
      console.error("Upload failed:", error);
      alert("Failed to upload to cloud. Please try again.");
    } finally {
      setUploadingStates(prev => ({ ...prev, [checklist.id]: false }));
      setUploadWarningDialog({ isOpen: false, checklist: null });
    }
  };

  const handleSyncAll = async () => {
    if (!user) return;

    setIsSyncing(true);
    try {
      // With Firestore persistence, sync is automatic
      // Just refresh the data to show latest state
      loadChecklists();
      alert("Data refreshed! Firestore automatically syncs your changes in real-time.");
    } catch (error) {
      console.error("Refresh failed:", error);
      alert("Failed to refresh data. Please try again.");
    } finally {
      setIsSyncing(false);
    }
  };

  const getSyncStatusBadge = (checklist: ChecklistData) => {
    const { status } = checklist.syncMetadata;
    
    // Show completion status first if completed
    if (checklist.isCompleted) {
      return (
        <Badge variant="outline" className="text-purple-600 border-purple-600">
          <Lock className="h-3 w-3 mr-1" />
          Completed
        </Badge>
      );
    }
    
    switch (status) {
      case 'synced':
        return (
          <Badge variant="outline" className="text-green-600 border-green-600">
            <CheckCircle className="h-3 w-3 mr-1" />
            Synced
          </Badge>
        );
      case 'ready-to-submit':
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-600">
            <Upload className="h-3 w-3 mr-1" />
            Ready to Submit
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="outline" className="text-yellow-600 border-yellow-600">
            <Upload className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case 'local-only':
        return (
          <Badge variant="outline" className="text-gray-600 border-gray-600">
            <Tag className="h-3 w-3 mr-1" />
            Local Only
          </Badge>
        );
      case 'conflict':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Conflict
          </Badge>
        );
      case 'error':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Error
          </Badge>
        );
      default:
        return null;
    }
  };

  const handleMarkAsReady = async (id: string) => {
    try {
      const checklist = await StorageService.getChecklistByIdAsync(id);
      if (checklist) {
        const updatedChecklist = {
          ...checklist,
          syncMetadata: {
            ...checklist.syncMetadata,
            status: 'ready-to-submit' as const
          }
        };
        const success = await StorageService.saveChecklist(updatedChecklist);
    if (success) {
      loadChecklists();
    } else {
          alert("Failed to mark checklist as ready. Please try again.");
        }
      }
    } catch (error) {
      console.error('Failed to mark as ready:', error);
      alert("Failed to mark checklist as ready. Please try again.");
    }
  };

  const handleUnmarkAsReady = async (id: string) => {
    try {
      const checklist = await StorageService.getChecklistByIdAsync(id);
      if (checklist) {
        const updatedChecklist = {
          ...checklist,
          syncMetadata: {
            ...checklist.syncMetadata,
            status: 'local-only' as const
          }
        };
        const success = await StorageService.saveChecklist(updatedChecklist);
    if (success) {
      loadChecklists();
    } else {
          alert("Failed to unmark checklist. Please try again.");
        }
      }
    } catch (error) {
      console.error('Failed to unmark as ready:', error);
      alert("Failed to unmark checklist. Please try again.");
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  // Mobile Card Component
  const InspectionCard = ({ checklist }: { checklist: ChecklistData }) => {
    const summary = calculateChecklistSummary(checklist);
    const canUpload = !checklist.isCompleted && (checklist.syncMetadata.status === 'local-only' || checklist.syncMetadata.status === 'pending');
    
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <CardTitle className="text-lg flex items-center gap-2">
                <Tag className="h-4 w-4" />
                {checklist.generalInfo.tagNo}
                {checklist.isCompleted && (
                  <div title="Completed - Admin access only">
                    <Lock className="h-4 w-4 text-purple-600" />
                  </div>
                )}
              </CardTitle>
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Building className="h-3 w-3" />
                {checklist.generalInfo.clientName}
              </div>
            </div>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/checklist?edit=${checklist.id}`)}
                disabled={checklist.isCompleted}
                title={checklist.isCompleted ? "Cannot edit completed checklists" : "Edit checklist"}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    disabled={checklist.isCompleted}
                    title={checklist.isCompleted ? "Cannot delete completed checklists" : "Delete checklist"}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Inspection</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete this inspection? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleDelete(checklist.id)}>
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Building:</span>
              <span>{checklist.generalInfo.building}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Location:</span>
              <span>{checklist.generalInfo.location}</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Date:</span>
              <span>{formatDate(checklist.generalInfo.date)}</span>
            </div>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Inspector:</span>
              <span>{checklist.generalInfo.inspectedBy}</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm text-muted-foreground">Status Summary:</div>
            <div className="flex gap-1 flex-wrap">
              <Badge variant="outline" className="text-green-600 border-green-600">
                {summary.totalOk} OK
              </Badge>
              {summary.totalFaulty > 0 && (
                <Badge variant="outline" className="text-red-600 border-red-600">
                  {summary.totalFaulty} Faulty
                </Badge>
              )}
              {summary.totalNA > 0 && (
                <Badge variant="outline" className="text-gray-600 border-gray-600">
                  {summary.totalNA} N/A
                </Badge>
              )}
              {summary.totalMissing > 0 && (
                <Badge variant="outline" className="text-orange-600 border-orange-600">
                  {summary.totalMissing} Missing
                </Badge>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm text-muted-foreground">Sync Status:</div>
            <div className="flex items-center justify-between">
              {getSyncStatusBadge(checklist)}
              <div className="flex gap-1">
                {/* Mark as Ready / Unmark buttons for mobile */}
                {checklist.syncMetadata.status === 'local-only' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleMarkAsReady(checklist.id)}
                    title="Mark as ready to submit"
                  >
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Ready
                  </Button>
                )}
                
                {checklist.syncMetadata.status === 'ready-to-submit' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUnmarkAsReady(checklist.id)}
                    title="Unmark (return to local-only)"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Unmark
                  </Button>
                )}

                {canUpload && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUploadToCloud(checklist)}
                    disabled={uploadingStates[checklist.id]}
                  >
                    {uploadingStates[checklist.id] ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Cloud className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport(checklist, 'excel')}
              disabled={exportingStates[checklist.id]?.excel}
              className="flex-1"
            >
              {exportingStates[checklist.id]?.excel ? (
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
              ) : (
                <FileSpreadsheet className="h-3 w-3 mr-1" />
              )}
              Excel
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport(checklist, 'pdf')}
              disabled={exportingStates[checklist.id]?.pdf}
              className="flex-1"
            >
              {exportingStates[checklist.id]?.pdf ? (
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
              ) : (
                <FileText className="h-3 w-3 mr-1" />
              )}
              PDF
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6 p-4">
      {/* Approval Message for Users */}
      <ApprovalMessage />
      
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">Saved Inspections</h1>
          <p className="text-muted-foreground">
            Manage your saved inspection checklists
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            onClick={handleSyncAll}
            disabled={isSyncing}
            variant="outline"
            className="flex items-center gap-2"
          >
            {isSyncing ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Sync All
          </Button>
          
          <Link href="/checklist">
            <Button className="w-full sm:w-auto flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Inspection
            </Button>
          </Link>
        </div>
      </div>

      {checklists.length === 0 ? (
        <Card className="text-center p-8">
          <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
            <Database className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No inspections found</h3>
          <p className="text-muted-foreground mb-4">
            You haven&apos;t saved any inspection checklists yet.
          </p>
          <Link href="/checklist">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Your First Inspection
            </Button>
          </Link>
        </Card>
      ) : (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">{checklists.length}</div>
                <p className="text-xs text-muted-foreground">Total Inspections</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-purple-600">
                  {checklists.filter(c => c.isCompleted).length}
                </div>
                <p className="text-xs text-muted-foreground">Completed</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-green-600">
                  {checklists.filter(c => c.syncMetadata.status === 'synced').length}
                </div>
                <p className="text-xs text-muted-foreground">Synced</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-blue-600">
                  {checklists.filter(c => c.syncMetadata.status === 'ready-to-submit').length}
                </div>
                <p className="text-xs text-muted-foreground">Ready to Submit</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-gray-600">
                  {checklists.filter(c => c.syncMetadata.status === 'local-only').length}
                </div>
                <p className="text-xs text-muted-foreground">Local Only</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-yellow-600">
                  {checklists.filter(c => c.syncMetadata.status === 'pending').length}
                </div>
                <p className="text-xs text-muted-foreground">Pending</p>
              </CardContent>
            </Card>
          </div>

          {/* Storage Management */}
          <StorageManager onStorageChange={loadChecklists} />

          {/* Sync Status */}
          <SyncStatus onSyncTriggered={loadChecklists} />

          {/* Mobile View */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:hidden">
            {checklists.map((checklist) => (
              <InspectionCard key={checklist.id} checklist={checklist} />
            ))}
          </div>

          {/* Desktop Table View */}
          <div className="hidden md:block">
            <Card>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tag No.</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Building</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Inspector</TableHead>
                    <TableHead>Sync Status</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {checklists.map((checklist) => {
                    const summary = calculateChecklistSummary(checklist);
                    const canUpload = !checklist.isCompleted && (checklist.syncMetadata.status === 'local-only' || checklist.syncMetadata.status === 'pending');
                    
                    return (
                      <TableRow key={checklist.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {checklist.generalInfo.tagNo}
                            {checklist.isCompleted && (
                              <div title="Completed - Admin access only">
                                <Lock className="h-4 w-4 text-purple-600" />
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{checklist.generalInfo.clientName}</TableCell>
                        <TableCell>{checklist.generalInfo.building}</TableCell>
                        <TableCell>{formatDate(checklist.generalInfo.date)}</TableCell>
                        <TableCell>{checklist.generalInfo.inspectedBy}</TableCell>
                        <TableCell>{getSyncStatusBadge(checklist)}</TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              {summary.totalOk} OK
                            </Badge>
                            {summary.totalFaulty > 0 && (
                              <Badge variant="outline" className="text-red-600 border-red-600">
                                {summary.totalFaulty} F
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-1">
                            {/* Mark as Ready / Unmark buttons for local-only checklists */}
                            {checklist.syncMetadata.status === 'local-only' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleMarkAsReady(checklist.id)}
                                title="Mark as ready to submit"
                                className="text-blue-600 hover:text-blue-700"
                              >
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                            )}
                            
                            {checklist.syncMetadata.status === 'ready-to-submit' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleUnmarkAsReady(checklist.id)}
                                title="Unmark (return to local-only)"
                                className="text-gray-600 hover:text-gray-700"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            )}

                            {canUpload && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleUploadToCloud(checklist)}
                                disabled={uploadingStates[checklist.id]}
                                title="Upload to cloud"
                              >
                                {uploadingStates[checklist.id] ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Cloud className="h-4 w-4" />
                                )}
                              </Button>
                            )}
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleExport(checklist, 'excel')}
                              disabled={exportingStates[checklist.id]?.excel}
                              title="Export to Excel"
                            >
                              {exportingStates[checklist.id]?.excel ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <FileSpreadsheet className="h-4 w-4" />
                              )}
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleExport(checklist, 'pdf')}
                              disabled={exportingStates[checklist.id]?.pdf}
                              title="Export to PDF"
                            >
                              {exportingStates[checklist.id]?.pdf ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <FileText className="h-4 w-4" />
                              )}
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => router.push(`/checklist?edit=${checklist.id}`)}
                              disabled={checklist.isCompleted}
                              title={checklist.isCompleted ? "Cannot edit completed checklists" : "Edit checklist"}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  disabled={checklist.isCompleted}
                                  title={checklist.isCompleted ? "Cannot delete completed checklists" : "Delete checklist"}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Inspection</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete this inspection? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => handleDelete(checklist.id)}>
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </Card>
          </div>

          {/* Clear All Button */}
          {checklists.length > 0 && (
            <div className="flex justify-center">
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="text-destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Clear All Inspections
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Clear All Inspections</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete all saved inspections? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleClearAll}>
                      Clear All
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </>
      )}
      
      {/* Upload Warning Dialog */}
      <AlertDialog open={uploadWarningDialog.isOpen} onOpenChange={(open) => 
        setUploadWarningDialog({ isOpen: open, checklist: null })
      }>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Confirm Upload to Cloud
            </AlertDialogTitle>
            <AlertDialogDescription>
              <strong>Warning:</strong> You are about to upload this checklist to the cloud.
            </AlertDialogDescription>
            <div className="space-y-3 text-sm text-muted-foreground">
              <div>
                Once uploaded, the checklist will be marked as <strong>completed</strong> and can only be modified or deleted by administrators.
              </div>
              <div>
                You will still be able to:
                <ul className="list-disc list-inside mt-1 ml-2">
                  <li>View the checklist</li>
                  <li>Export to PDF/Excel</li>
                  <li>Remove from browser storage (local copy only)</li>
                </ul>
              </div>
              <div className="font-medium">
                Are you sure you want to continue?
              </div>
            </div>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => 
              setUploadWarningDialog({ isOpen: false, checklist: null })
            }>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmUploadToCloud}
              className="bg-amber-600 hover:bg-amber-700"
            >
              Upload to Cloud
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

export default function SavedInspectionsPage() {
  return (
    <AuthGuard>
      <SavedInspectionsPageContent />
    </AuthGuard>
  );
} 