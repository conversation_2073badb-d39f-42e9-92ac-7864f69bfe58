'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from './auth';
import { MigrationDialog } from './migration/migration-dialog';
import { MigrationService } from '@/lib/migration-service';
import { StorageService } from '@/lib/storage-adapter';
import { log } from '@/lib/logger';

interface AppInitializationProps {
  children: React.ReactNode;
}

export function AppInitialization({ children }: AppInitializationProps) {
  const { user, loading: authLoading } = useAuth();
  const [showMigrationDialog, setShowMigrationDialog] = useState(false);
  const [storageInitialized, setStorageInitialized] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);

  // Initialize storage when user is authenticated
  useEffect(() => {
    if (!authLoading && user) {
      initializeStorage();
    } else if (!authLoading && !user) {
      // Reset initialization state when user logs out
      setStorageInitialized(false);
      setInitializationError(null);
    }
  }, [user, authLoading]);

  // Check for migration needs when storage is initialized
  useEffect(() => {
    if (storageInitialized && user) {
      checkMigrationNeeded();
    }
  }, [storageInitialized, user]);

  // Listen for manual migration triggers
  useEffect(() => {
    const handleTriggerMigration = () => {
      if (user && !MigrationService.isFirestorePersistenceEnabled()) {
        log.info('Manual migration triggered', 'APP_INIT', { userId: user.uid });
        setShowMigrationDialog(true);
      }
    };

    window.addEventListener('triggerMigration', handleTriggerMigration);
    
    return () => {
      window.removeEventListener('triggerMigration', handleTriggerMigration);
    };
  }, [user]);

  const initializeStorage = async () => {
    if (!user) return;

    try {
      log.info('Initializing storage service', 'APP_INIT', { userId: user.uid });
      
      await StorageService.initialize(user.uid);
      setStorageInitialized(true);
      setInitializationError(null);
      
      log.info('Storage service initialized successfully', 'APP_INIT', { userId: user.uid });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
      setInitializationError(errorMessage);
      
      log.error('Failed to initialize storage service', 'APP_INIT', {
        userId: user.uid,
        error: errorMessage
      });
    }
  };

  const checkMigrationNeeded = () => {
    if (!user) return;

    try {
      const migrationNeeded = MigrationService.isMigrationNeeded();
      const firestoreEnabled = MigrationService.isFirestorePersistenceEnabled();
      
      log.debug('Migration status check', 'APP_INIT', {
        userId: user.uid,
        migrationNeeded,
        firestoreEnabled
      });

      if (migrationNeeded && !firestoreEnabled) {
        log.info('Migration needed, showing dialog', 'APP_INIT', { userId: user.uid });
        setShowMigrationDialog(true);
      }
      
    } catch (error) {
      log.error('Failed to check migration status', 'APP_INIT', {
        userId: user.uid,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  const handleMigrationComplete = () => {
    log.info('Migration completed, reinitializing storage', 'APP_INIT', { userId: user?.uid });
    setShowMigrationDialog(false);
    
    // Reinitialize storage after migration
    if (user) {
      initializeStorage();
    }
  };

  const handleMigrationSkip = () => {
    log.info('Migration skipped by user', 'APP_INIT', { userId: user?.uid });
    setShowMigrationDialog(false);
  };

  // Show loading during auth or initialization
  if (authLoading || (user && !storageInitialized && !initializationError)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-sm text-muted-foreground">
            {authLoading ? 'Authenticating...' : 'Initializing storage...'}
          </p>
        </div>
      </div>
    );
  }

  // Show initialization error if occurred
  if (initializationError) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 max-w-md text-center">
          <h3 className="text-lg font-semibold text-destructive mb-2">
            Initialization Error
          </h3>
          <p className="text-sm text-muted-foreground mb-4">
            {initializationError}
          </p>
          <button
            onClick={() => {
              setInitializationError(null);
              if (user) initializeStorage();
            }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      {children}
      
      {/* Migration Dialog */}
      {user && (
        <MigrationDialog
          isOpen={showMigrationDialog}
          onClose={handleMigrationSkip}
          userId={user.uid}
          onComplete={handleMigrationComplete}
        />
      )}
    </>
  );
} 