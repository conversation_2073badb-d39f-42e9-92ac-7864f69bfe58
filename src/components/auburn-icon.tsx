interface AuburnIconProps {
  className?: string;
  size?: number;
}

export function AuburnIcon({ className = "", size = 24 }: AuburnIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 200 200"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="200" height="200" fill="#000000" rx="20" />
      <g>
        <g 
          fill="#79ca9f" 
          transform="matrix(9.002197802197802,0,0,9.002197802197802,38.64861741648926,164.00500244203505)" 
          stroke="#79ca9f" 
          strokeWidth="0"
        >
          <path d="M9.98 0L9.15-2.66L4.45-2.66L3.63 0L-0.03 0L5.19-14.22L8.41-14.22L13.66 0L9.98 0ZM6.80-10.23L5.27-5.30L8.33-5.30L6.80-10.23Z" />
        </g>
      </g>
    </svg>
  );
} 