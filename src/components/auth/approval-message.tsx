'use client';

import React from 'react';
import { useAuth } from './auth-provider';
import { UserRole } from '@/types/user';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Clock, AlertTriangle } from 'lucide-react';

interface ApprovalMessageProps {
  className?: string;
}

export function ApprovalMessage({ className }: ApprovalMessageProps) {
  const { user, extendedUser, loading } = useAuth();

  // Don't show anything while loading
  if (loading) {
    return null;
  }

  // If user is authenticated but no Firestore document exists
  if (user && !extendedUser) {
    return (
      <Alert className={`border-red-200 bg-red-50 dark:bg-red-950/20 dark:border-red-800 ${className || ''}`}>
        <AlertTriangle className="h-4 w-4 text-red-600" />
        <AlertTitle className="text-red-800 dark:text-red-200">Account Setup Required</AlertTitle>
        <AlertDescription className="text-red-700 dark:text-red-300">
          Your account setup is incomplete. Please contact an administrator to complete your account setup, or try signing out and signing back in.
          <div className="mt-3 flex items-center gap-2 text-sm">
            <AlertTriangle className="h-4 w-4" />
            <span>If this problem persists, please contact support.</span>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Only show approval message for users with 'user' role
  if (extendedUser && extendedUser.role === UserRole.USER) {
    return (
      <Alert className={`border-amber-200 bg-amber-50 dark:bg-amber-950/20 dark:border-amber-800 ${className || ''}`}>
        <AlertCircle className="h-4 w-4 text-amber-600" />
        <AlertTitle className="text-amber-800 dark:text-amber-200">Account Pending Approval</AlertTitle>
        <AlertDescription className="text-amber-700 dark:text-amber-300">
          Your account is currently awaiting approval from an administrator. While you wait, please ensure your profile information is complete. 
          You will receive full access to all features once an admin approves your account and updates your role.
          <div className="mt-3 flex items-center gap-2 text-sm">
            <Clock className="h-4 w-4" />
            <span>Please be patient while we review your request.</span>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // No message for other roles or unauthenticated users
  return null;
} 