'use client';

import React, { useState } from 'react';
import { useAuth } from './auth-provider';
import { AuthModal } from './auth-modal';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Shield, Lock } from 'lucide-react';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showModal?: boolean;
}

export function AuthGuard({ children, fallback, showModal = true }: AuthGuardProps) {
  const { user, loading } = useAuth();
  const [showAuthModal, setShowAuthModal] = useState(false);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <>
        <div className="flex items-center justify-center min-h-screen p-4">
          <Card className="w-full max-w-md p-8 text-center space-y-6">
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
              <Shield className="h-8 w-8 text-primary" />
            </div>
            
            <div className="space-y-3">
              <h2 className="text-2xl font-bold tracking-tight">Authentication Required</h2>
              <p className="text-muted-foreground">
                You need to be signed in to access this feature. Sign in or create an account to continue.
              </p>
            </div>

            <div className="space-y-3">
              <Button 
                onClick={() => setShowAuthModal(true)}
                className="w-full"
              >
                <Lock className="h-4 w-4 mr-2" />
                Sign In
              </Button>
              <Button 
                variant="outline"
                onClick={() => setShowAuthModal(true)}
                className="w-full"
              >
                Create Account
              </Button>
            </div>
          </Card>
        </div>

        {showModal && (
          <AuthModal
            isOpen={showAuthModal}
            onClose={() => setShowAuthModal(false)}
          />
        )}
      </>
    );
  }

  return <>{children}</>;
}

// Hook for conditional rendering based on auth state
export function useAuthGuard() {
  const { user, loading } = useAuth();
  
  return {
    isAuthenticated: !!user,
    isLoading: loading,
    user,
  };
} 