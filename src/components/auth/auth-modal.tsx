'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle, VisuallyHidden } from '@/components/ui';
import { SignInForm } from './sign-in-form';
import { SignUpForm } from './sign-up-form';
import { ResetPasswordForm } from './reset-password-form';

type AuthModalView = 'signin' | 'signup' | 'reset';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultView?: AuthModalView;
}

export function AuthModal({ isOpen, onClose, defaultView = 'signin' }: AuthModalProps) {
  const [currentView, setCurrentView] = useState<AuthModalView>(defaultView);

  const handleSuccess = () => {
    onClose();
    // Reset to default view for next time
    setTimeout(() => setCurrentView('signin'), 300);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
      // Reset to default view when modal is closed
      setTimeout(() => setCurrentView('signin'), 300);
    }
  };

  const getModalTitle = () => {
    switch (currentView) {
      case 'signup':
        return 'Create Account';
      case 'reset':
        return 'Reset Password';
      default:
        return 'Sign In';
    }
  };

  const renderContent = () => {
    switch (currentView) {
      case 'signup':
        return (
          <SignUpForm
            onSwitchToSignIn={() => setCurrentView('signin')}
            onSuccess={handleSuccess}
          />
        );
      case 'reset':
        return (
          <ResetPasswordForm
            onBack={() => setCurrentView('signin')}
          />
        );
      default:
        return (
          <SignInForm
            onSwitchToSignUp={() => setCurrentView('signup')}
            onSwitchToReset={() => setCurrentView('reset')}
            onSuccess={handleSuccess}
          />
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md p-0 bg-transparent border-none shadow-none">
        <VisuallyHidden>
          <DialogTitle>{getModalTitle()}</DialogTitle>
        </VisuallyHidden>
        <div className="relative">
          {renderContent()}
        </div>
      </DialogContent>
    </Dialog>
  );
} 