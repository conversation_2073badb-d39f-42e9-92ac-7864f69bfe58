'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { AuthContextType, AuthUser } from '@/types/auth';
import { ExtendedUser } from '@/types/user';
import { AuthService } from '@/lib/auth';
import { AnalyticsService } from '@/lib/analytics';
import { UserService } from '@/lib/user-service';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [extendedUser, setExtendedUser] = useState<ExtendedUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = AuthService.onAuthStateChanged(async (user) => {
      setUser(user);
      
      // Get extended user data if user exists
      if (user) {
        try {
          console.log('Fetching extended user data for:', user.uid);
          const extendedUserData = await UserService.getExtendedUser(user);
          if (extendedUserData) {
            console.log('Extended user data loaded:', extendedUserData);
            setExtendedUser(extendedUserData);
          } else {
            console.warn('No Firestore user document found for user:', user.uid);
            console.log('Attempting to create missing user document as fallback...');
            
            // Try to create missing user document as fallback
            try {
              await UserService.createUserDocument(user);
              console.log('User document created successfully');
              
              // Retry fetching extended user data after creation
              const retryExtendedUserData = await UserService.getExtendedUser(user);
              if (retryExtendedUserData) {
                console.log('Extended user data loaded after document creation:', retryExtendedUserData);
                setExtendedUser(retryExtendedUserData);
              } else {
                console.error('Failed to load extended user data even after document creation');
                setExtendedUser(null);
              }
            } catch (createError) {
              console.error('Failed to create user document:', createError);
              setExtendedUser(null);
            }
          }
        } catch (error) {
          console.error('Error fetching extended user data:', error);
          setExtendedUser(null);
        }
      } else {
        setExtendedUser(null);
      }
      
      setLoading(false);
      
      // Set analytics user properties
      if (user) {
        AnalyticsService.setUserId(user.uid);
        AnalyticsService.setUserProperties({
          user_id: user.uid,
          email_verified: user.emailVerified,
        });
      }
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string): Promise<void> => {
    try {
      await AuthService.signIn(email, password);
      AnalyticsService.logSignIn('email');
    } catch (error) {
      throw error;
    }
  };

  const signUp = async (email: string, password: string, displayName?: string): Promise<void> => {
    try {
      console.log('Starting user registration process...');
      await AuthService.signUp(email, password, displayName);
      console.log('User registration completed, Firebase Function should create user document');
      AnalyticsService.logSignUp('email');
    } catch (error) {
      throw error;
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      await AuthService.signOut();
      AnalyticsService.logEvent('logout');
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (email: string): Promise<void> => {
    try {
      await AuthService.resetPassword(email);
      AnalyticsService.logEvent('password_reset_request');
    } catch (error) {
      throw error;
    }
  };

  const updateProfile = async (displayName: string, photoURL?: string): Promise<void> => {
    try {
      await AuthService.updateProfile(displayName, photoURL);
      AnalyticsService.logEvent('profile_updated');
    } catch (error) {
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    extendedUser,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
} 