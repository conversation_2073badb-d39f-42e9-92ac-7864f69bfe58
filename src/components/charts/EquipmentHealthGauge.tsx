'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus, Shield } from 'lucide-react';

interface EquipmentHealthGaugeProps {
  score: number;
  grade: string;
  color: string;
  trend: 'up' | 'down' | 'stable';
  className?: string;
}

interface GaugeProps {
  score: number;
  color: string;
  size?: number;
}

const Gauge: React.FC<GaugeProps> = ({ score, color, size = 200 }) => {
  const radius = (size - 40) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (score / 100) * circumference;
  
  // Create gradient colors based on score
  const getGradientId = (score: number) => {
    if (score >= 95) return 'excellent-gradient';
    if (score >= 85) return 'good-gradient';
    if (score >= 70) return 'fair-gradient';
    if (score >= 50) return 'poor-gradient';
    return 'critical-gradient';
  };

  const gradientId = getGradientId(score);

  return (
    <div className="relative flex items-center justify-center" style={{ width: size, height: size }}>
      <svg width={size} height={size} className="transform -rotate-90">
        <defs>
          <linearGradient id="excellent-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#10b981" />
            <stop offset="100%" stopColor="#059669" />
          </linearGradient>
          <linearGradient id="good-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#22c55e" />
            <stop offset="100%" stopColor="#16a34a" />
          </linearGradient>
          <linearGradient id="fair-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#f59e0b" />
            <stop offset="100%" stopColor="#d97706" />
          </linearGradient>
          <linearGradient id="poor-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#ef4444" />
            <stop offset="100%" stopColor="#dc2626" />
          </linearGradient>
          <linearGradient id="critical-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#dc2626" />
            <stop offset="100%" stopColor="#b91c1c" />
          </linearGradient>
        </defs>
        
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="rgba(156, 163, 175, 0.2)"
          strokeWidth="8"
          fill="transparent"
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={`url(#${gradientId})`}
          strokeWidth="8"
          fill="transparent"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ duration: 2, ease: "easeOut" }}
          style={{
            filter: 'drop-shadow(0 0 6px rgba(59, 130, 246, 0.5))'
          }}
        />
      </svg>
      
      {/* Center content */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            className="text-4xl font-bold text-foreground"
          >
            {score}
          </motion.div>
          <div className="text-sm text-muted-foreground">Health Score</div>
        </div>
      </div>
    </div>
  );
};

export function EquipmentHealthGauge({ 
  score, 
  grade, 
  color, 
  trend, 
  className = '' 
}: EquipmentHealthGaugeProps) {
  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus;
  const trendColor = trend === 'up' ? 'text-green-500' : trend === 'down' ? 'text-red-500' : 'text-gray-500';
  const trendText = trend === 'up' ? 'Improving' : trend === 'down' ? 'Declining' : 'Stable';

  const getGradeColor = (grade: string) => {
    switch (grade.toLowerCase()) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-green-100 text-green-700 border-green-200';
      case 'fair': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'poor': return 'bg-red-100 text-red-800 border-red-200';
      case 'critical': return 'bg-red-100 text-red-900 border-red-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRecommendation = (score: number, grade: string) => {
    if (score >= 95) return "Excellent health! Continue current maintenance practices.";
    if (score >= 85) return "Good health with minor issues. Schedule routine maintenance.";
    if (score >= 70) return "Fair health. Consider preventive maintenance actions.";
    if (score >= 50) return "Poor health detected. Immediate attention required.";
    return "Critical issues found. Emergency maintenance needed.";
  };

  const getHealthMetrics = (score: number) => {
    return [
      {
        label: 'Reliability',
        value: Math.min(100, score + 5),
        color: 'bg-blue-500'
      },
      {
        label: 'Performance',
        value: score,
        color: 'bg-green-500'
      },
      {
        label: 'Safety',
        value: Math.max(0, score - 5),
        color: 'bg-purple-500'
      }
    ];
  };

  const healthMetrics = getHealthMetrics(score);

  return (
    <Card className={`glass border-border/50 ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-500" />
              Equipment Health
            </CardTitle>
            <CardDescription>
              Overall system performance and reliability
            </CardDescription>
          </div>
          <Badge variant="outline" className={getGradeColor(grade)}>
            {grade}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="flex flex-col lg:flex-row items-center gap-6">
          {/* Gauge */}
          <div className="flex-shrink-0">
            <Gauge score={score} color={color} size={200} />
          </div>
          
          {/* Health Details */}
          <div className="flex-1 space-y-4 w-full">
            {/* Trend Indicator */}
            <div className="flex items-center gap-2">
              <div className={`flex items-center gap-1 ${trendColor}`}>
                <TrendIcon className="h-4 w-4" />
                <span className="text-sm font-medium">{trendText}</span>
              </div>
              <span className="text-sm text-muted-foreground">trend over recent inspections</span>
            </div>
            
            {/* Health Metrics */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-foreground">Health Breakdown</h4>
              {healthMetrics.map((metric, index) => (
                <motion.div
                  key={metric.label}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="space-y-1"
                >
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{metric.label}</span>
                    <span className="font-medium">{metric.value}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className={`${metric.color} h-2 rounded-full`}
                      initial={{ width: 0 }}
                      animate={{ width: `${metric.value}%` }}
                      transition={{ delay: 0.5 + index * 0.1, duration: 0.8 }}
                    />
                  </div>
                </motion.div>
              ))}
            </div>
            
            {/* Recommendation */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1 }}
              className="bg-muted/50 rounded-lg p-3"
            >
              <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                💡 Recommendation
              </h4>
              <p className="text-sm text-muted-foreground">
                {getRecommendation(score, grade)}
              </p>
            </motion.div>
          </div>
        </div>
        
        {/* Score Ranges */}
        <div className="mt-6 pt-4 border-t border-border">
          <div className="grid grid-cols-5 gap-2 text-xs">
            <div className="text-center">
              <div className="w-full h-2 bg-red-500 rounded mb-1"></div>
              <span className="text-muted-foreground">0-49</span>
              <div className="font-medium text-red-600">Critical</div>
            </div>
            <div className="text-center">
              <div className="w-full h-2 bg-orange-500 rounded mb-1"></div>
              <span className="text-muted-foreground">50-69</span>
              <div className="font-medium text-orange-600">Poor</div>
            </div>
            <div className="text-center">
              <div className="w-full h-2 bg-yellow-500 rounded mb-1"></div>
              <span className="text-muted-foreground">70-84</span>
              <div className="font-medium text-yellow-600">Fair</div>
            </div>
            <div className="text-center">
              <div className="w-full h-2 bg-green-500 rounded mb-1"></div>
              <span className="text-muted-foreground">85-94</span>
              <div className="font-medium text-green-600">Good</div>
            </div>
            <div className="text-center">
              <div className="w-full h-2 bg-emerald-500 rounded mb-1"></div>
              <span className="text-muted-foreground">95-100</span>
              <div className="font-medium text-emerald-600">Excellent</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 