"use client";

import { UseFormReturn, FieldPath } from "react-hook-form";
import { ChecklistFormData } from "@/lib/validation";
import { ChecklistFieldConfig, CheckStatus } from "@/types/checklist";
import { CHECK_STATUS_OPTIONS } from "@/config/checklist-fields";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface CheckFieldProps {
  form: UseFormReturn<ChecklistFormData>;
  field: ChecklistFieldConfig;
  section: 'mechanical' | 'electrical' | 'sequence';
}

export function CheckField({ form, field, section }: CheckFieldProps) {
  const getFieldName = (): FieldPath<ChecklistFormData> => {
    const sectionKey = section === 'sequence' ? 'sequenceControlsChecks' : `${section}Checks`;
    return `${sectionKey}.${field.key}` as FieldPath<ChecklistFormData>;
  };

  const fieldName = getFieldName();

  if (field.type === 'number') {
    return (
      <FormField
        control={form.control}
        name={fieldName}
        render={({ field: formField }) => (
          <FormItem>
            <FormLabel className="text-sm">
              {field.label}
              {field.unit && (
                <span className="text-muted-foreground ml-1">({field.unit})</span>
              )}
            </FormLabel>
            <FormControl>
              <Input
                type="number"
                step="any"
                placeholder={`Enter value in ${field.unit || 'units'}`}
                {...formField}
                onChange={(e) => {
                  const value = e.target.value;
                  formField.onChange(value === '' ? undefined : parseFloat(value));
                }}
                value={typeof formField.value === 'number' ? formField.value.toString() : ''}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  }

  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field: formField }) => (
        <FormItem>
          <FormLabel className="text-sm">{field.label}</FormLabel>
          <Select
            onValueChange={(value) => formField.onChange(value as CheckStatus)}
            value={typeof formField.value === 'string' ? formField.value : undefined}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {CHECK_STATUS_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(option.value)}`}>
                    {option.label}
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

function getStatusColor(status: CheckStatus): string {
  switch (status) {
    case 'OK':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'Faulty':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    case 'N/A':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    case 'Missing':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
} 