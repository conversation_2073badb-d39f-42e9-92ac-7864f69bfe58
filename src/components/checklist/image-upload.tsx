"use client";

import { useState, useRef } from "react";
import { UseFormReturn } from "react-hook-form";
import { ChecklistFormData } from "@/lib/validation";
import { 
  compressImage, 
  quickCompress, 
  validateImageFile, 
  formatFileSize,
  getRecommendedProfile,
  CompressionResult 
} from "@/lib/image-compression";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Upload, X, Camera, PenTool, Zap, Info } from "lucide-react";
import Image from "next/image";

interface ImageUploadProps {
  form: UseFormReturn<ChecklistFormData>;
}

export function ImageUpload({ form }: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [compressionProgress, setCompressionProgress] = useState(0);
  const [compressionInfo, setCompressionInfo] = useState<{
    originalSize?: number;
    compressedSize?: number;
    compressionRatio?: number;
    fieldName?: string;
  }>({});
  const [showCompressionDetails, setShowCompressionDetails] = useState(false);
  
  const beforeImageRef = useRef<HTMLInputElement>(null);
  const afterImageRef = useRef<HTMLInputElement>(null);
  const signatureRef = useRef<HTMLInputElement>(null);

  const beforeImage = form.watch("beforeImage");
  const afterImage = form.watch("afterImage");
  const inspectorSignature = form.watch("inspectorSignature");

  const handleImageUpload = async (
    file: File,
    fieldName: "beforeImage" | "afterImage" | "inspectorSignature"
  ) => {
    if (!file) return;

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    setIsUploading(true);
    setCompressionProgress(0);
    setCompressionInfo({ fieldName });

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setCompressionProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      let compressionResult: CompressionResult;

      // Use appropriate compression based on field type
      if (fieldName === "inspectorSignature") {
        compressionResult = await quickCompress.signature(file);
      } else {
        // Auto-select profile based on device/connection
        const recommendedProfile = getRecommendedProfile();
        
        if (recommendedProfile === 'mobile') {
          compressionResult = await quickCompress.mobile(file);
        } else {
          compressionResult = await quickCompress.standard(file);
        }
      }

      clearInterval(progressInterval);
      setCompressionProgress(100);

      // Update compression info for display
      setCompressionInfo({
        fieldName,
        originalSize: compressionResult.originalSize,
        compressedSize: compressionResult.compressedSize,
        compressionRatio: compressionResult.compressionRatio
      });

      // Set the compressed image
      form.setValue(fieldName, compressionResult.base64);

      // Show compression details briefly
      setShowCompressionDetails(true);
      setTimeout(() => setShowCompressionDetails(false), 5000);

    } catch (error) {
      console.error("Error compressing image:", error);
      alert("Failed to process image. Please try again with a different image.");
    } finally {
      setIsUploading(false);
      setTimeout(() => {
        setCompressionProgress(0);
        setCompressionInfo({});
      }, 2000);
    }
  };

  const removeImage = (fieldName: "beforeImage" | "afterImage" | "inspectorSignature") => {
    form.setValue(fieldName, undefined);
    if (fieldName === "beforeImage" && beforeImageRef.current) {
      beforeImageRef.current.value = "";
    }
    if (fieldName === "afterImage" && afterImageRef.current) {
      afterImageRef.current.value = "";
    }
    if (fieldName === "inspectorSignature" && signatureRef.current) {
      signatureRef.current.value = "";
    }
  };

  return (
    <div className="space-y-6">
      {/* Compression Status */}
      {(isUploading || showCompressionDetails) && (
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">
                  {isUploading ? "Compressing Image..." : "Compression Complete"}
                </span>
              </div>
              
              {isUploading && (
                <div className="space-y-2">
                  <Progress value={compressionProgress} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    Optimizing image for faster upload and storage
                  </p>
                </div>
              )}

              {showCompressionDetails && compressionInfo.originalSize && (
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Info className="h-3 w-3" />
                    <span>
                      Reduced from {formatFileSize(compressionInfo.originalSize)} to{" "}
                      {formatFileSize(compressionInfo.compressedSize || 0)}
                    </span>
                  </div>
                  <div className="text-green-600 font-medium">
                    {compressionInfo.compressionRatio?.toFixed(1)}% smaller
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Image Upload
          </CardTitle>
          <CardDescription>
            Upload before and after images of the inspection. Images are automatically compressed for optimal performance.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Before Image */}
          <div className="space-y-3">
            <Label htmlFor="beforeImage" className="text-sm font-medium">
              Before Image
            </Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
              {beforeImage ? (
                <div className="space-y-3">
                  <div className="relative max-w-xs mx-auto">
                    <Image
                      src={beforeImage}
                      alt="Before inspection"
                      width={300}
                      height={200}
                      className="rounded-lg object-cover w-full h-48"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2"
                      onClick={() => removeImage("beforeImage")}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground text-center">
                    Before image uploaded successfully
                  </p>
                </div>
              ) : (
                <div className="text-center space-y-3">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Click to upload before image</p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG, WebP up to 20MB (auto-compressed to ~500KB)
                    </p>
                  </div>
                  <Input
                    ref={beforeImageRef}
                    id="beforeImage"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleImageUpload(file, "beforeImage");
                    }}
                    disabled={isUploading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => beforeImageRef.current?.click()}
                    disabled={isUploading}
                  >
                    {isUploading ? "Uploading..." : "Choose File"}
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* After Image */}
          <div className="space-y-3">
            <Label htmlFor="afterImage" className="text-sm font-medium">
              After Image
            </Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
              {afterImage ? (
                <div className="space-y-3">
                  <div className="relative max-w-xs mx-auto">
                    <Image
                      src={afterImage}
                      alt="After inspection"
                      width={300}
                      height={200}
                      className="rounded-lg object-cover w-full h-48"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2"
                      onClick={() => removeImage("afterImage")}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground text-center">
                    After image uploaded successfully
                  </p>
                </div>
              ) : (
                <div className="text-center space-y-3">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Click to upload after image</p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG, WebP up to 20MB (auto-compressed to ~500KB)
                    </p>
                  </div>
                  <Input
                    ref={afterImageRef}
                    id="afterImage"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleImageUpload(file, "afterImage");
                    }}
                    disabled={isUploading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => afterImageRef.current?.click()}
                    disabled={isUploading}
                  >
                    {isUploading ? "Uploading..." : "Choose File"}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inspector Signature */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PenTool className="h-5 w-5" />
            Inspector Signature
          </CardTitle>
          <CardDescription>
            Upload inspector signature (optional)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label htmlFor="inspectorSignature" className="text-sm font-medium">
              Signature Image
            </Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
              {inspectorSignature ? (
                <div className="space-y-3">
                  <div className="relative max-w-xs mx-auto">
                    <Image
                      src={inspectorSignature}
                      alt="Inspector signature"
                      width={300}
                      height={150}
                      className="rounded-lg object-contain w-full h-32 bg-white border"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2"
                      onClick={() => removeImage("inspectorSignature")}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground text-center">
                    Signature uploaded successfully
                  </p>
                </div>
              ) : (
                <div className="text-center space-y-3">
                  <PenTool className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Click to upload signature</p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG up to 20MB (auto-compressed to ~200KB, Optional)
                    </p>
                  </div>
                  <Input
                    ref={signatureRef}
                    id="inspectorSignature"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleImageUpload(file, "inspectorSignature");
                    }}
                    disabled={isUploading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => signatureRef.current?.click()}
                    disabled={isUploading}
                  >
                    {isUploading ? "Uploading..." : "Choose File"}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 