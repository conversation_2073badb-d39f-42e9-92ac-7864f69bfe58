"use client";

import { useState, useEffect } from "react";
import { Scanner } from "@yudiel/react-qr-scanner";
import { EquipmentTagService } from "@/lib/equipment-tag-service";
import { EquipmentTag } from "@/types/equipment-tag";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  QrCode, 
  Camera, 
  X, 
  Loader2, 
  AlertTriangle, 
  CheckCircle,
  Scan
} from "lucide-react";

interface QRScannerProps {
  onScanSuccess: (equipmentTag: EquipmentTag) => void;
  onError?: (error: string) => void;
  isOpen: boolean;
  onClose: () => void;
}

export function QRScanner({ onScanSuccess, onError, isOpen, onClose }: QRScannerProps) {
  const [isScanning, setIsScanning] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scannedData, setScannedData] = useState<string | null>(null);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setError(null);
      setScannedData(null);
      setIsValidating(false);
      setIsScanning(true);
    } else {
      setIsScanning(false);
    }
  }, [isOpen]);

  const handleScan = async (result: any[]) => {
    if (!result || result.length === 0 || isValidating) return;

    const qrContent = result[0]?.rawValue;
    if (!qrContent) return;

    setScannedData(qrContent);
    setIsScanning(false);
    setIsValidating(true);
    setError(null);

    try {
      const validation = await EquipmentTagService.validateQRCodeTag(qrContent);

      if (validation.isValid && validation.equipmentTag) {
        // Success - close dialog and pass the equipment tag
        setTimeout(() => {
          onScanSuccess(validation.equipmentTag!);
          onClose();
        }, 1000); // Short delay to show success state
      } else {
        setError(validation.error || 'Invalid QR code');
        setIsValidating(false);
        // Allow retry after error
        setTimeout(() => {
          setError(null);
          setScannedData(null);
          setIsScanning(true);
        }, 3000);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to validate QR code';
      setError(errorMessage);
      setIsValidating(false);
      onError?.(errorMessage);
      
      // Allow retry after error
      setTimeout(() => {
        setError(null);
        setScannedData(null);
        setIsScanning(true);
      }, 3000);
    }
  };

  const handleScanError = (error: unknown) => {
    console.error('QR Scanner error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Camera access failed';
    setError(errorMessage);
    onError?.(errorMessage);
  };

  const handleRetry = () => {
    setError(null);
    setScannedData(null);
    setIsValidating(false);
    setIsScanning(true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            Scan Equipment QR Code
          </DialogTitle>
          <DialogDescription>
            Point your camera at the equipment QR code to automatically load inspection details.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Scanner Container */}
          <Card>
            <CardContent className="p-4">
              <div className="relative aspect-square w-full max-w-sm mx-auto overflow-hidden rounded-lg bg-black">
                {isScanning && (
                  <Scanner
                    onScan={handleScan}
                    onError={handleScanError}
                    formats={['qr_code']}
                    components={{
                      finder: true,
                      torch: true,
                    }}
                    styles={{
                      container: { 
                        width: '100%', 
                        height: '100%',
                      },
                      video: { 
                        width: '100%', 
                        height: '100%',
                        objectFit: 'cover',
                      },
                    }}
                  />
                )}

                {/* Overlay for different states */}
                {!isScanning && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    {isValidating && (
                      <div className="text-center text-white">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                        <p className="text-sm">Validating QR code...</p>
                      </div>
                    )}
                    
                    {error && (
                      <div className="text-center text-white">
                        <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-red-400" />
                        <p className="text-sm">{error}</p>
                      </div>
                    )}

                    {scannedData && !error && !isValidating && (
                      <div className="text-center text-white">
                        <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-400" />
                        <p className="text-sm">QR code validated!</p>
                      </div>
                    )}
                  </div>
                )}

                {/* Scanner frame overlay */}
                {isScanning && (
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute inset-4 border-2 border-white border-opacity-50 rounded-lg">
                      <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-blue-500 rounded-tl-lg"></div>
                      <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-blue-500 rounded-tr-lg"></div>
                      <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-blue-500 rounded-bl-lg"></div>
                      <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-blue-500 rounded-br-lg"></div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Status Messages */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}

          {scannedData && !error && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                QR code scanned successfully! Validating equipment tag...
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between gap-2">
            <Button variant="outline" onClick={onClose}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            
            {error && (
              <Button onClick={handleRetry}>
                <Scan className="h-4 w-4 mr-2" />
                Retry Scan
              </Button>
            )}
          </div>

          {/* Instructions */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Instructions</CardTitle>
            </CardHeader>
            <CardContent className="text-xs text-muted-foreground space-y-1">
              <p>• Position the QR code within the frame</p>
              <p>• Ensure good lighting for better scanning</p>
              <p>• Hold steady until the code is detected</p>
              <p>• Only Auburn Engineering equipment tags are accepted</p>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
} 