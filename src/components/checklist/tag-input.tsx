"use client";

import { useState, useEffect, useRef } from "react";
import { EquipmentTagService } from "@/lib/equipment-tag-service";
import { EquipmentTag } from "@/types/equipment-tag";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Loader2, 
  X, 
  CheckCircle, 
  AlertTriangle,
  Tag as TagIcon,
  Building,
  MapPin,
  Package
} from "lucide-react";

interface TagInputProps {
  onTagSelect: (equipmentTag: EquipmentTag) => void;
  onError?: (error: string) => void;
  placeholder?: string;
  disabled?: boolean;
  selectedTag?: EquipmentTag | null;
  onClear?: () => void;
}

export function TagInput({ 
  onTagSelect, 
  onError, 
  placeholder = "Enter equipment tag number...",
  disabled = false,
  selectedTag = null,
  onClear
}: TagInputProps) {
  const [inputValue, setInputValue] = useState("");
  const [searchResults, setSearchResults] = useState<EquipmentTag[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update input value when selectedTag changes
  useEffect(() => {
    if (selectedTag) {
      setInputValue(selectedTag.tagNumber);
      setSearchResults([]);
      setShowDropdown(false);
      setError(null);
    } else {
      setInputValue("");
      setError(null);
    }
  }, [selectedTag]);

  // Handle search with debounce
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (inputValue.trim() && !selectedTag) {
      searchTimeoutRef.current = setTimeout(async () => {
        setIsSearching(true);
        setError(null);
        
        try {
          const results = await EquipmentTagService.searchEquipmentTagsByTagNumber(inputValue.trim(), 5);
          setSearchResults(results);
          setShowDropdown(results.length > 0);
          setHighlightedIndex(-1);
        } catch (err) {
          console.error('Search error:', err);
          setSearchResults([]);
          setShowDropdown(false);
        } finally {
          setIsSearching(false);
        }
      }, 300); // 300ms debounce
    } else {
      setSearchResults([]);
      setShowDropdown(false);
      setIsSearching(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [inputValue, selectedTag]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (value: string) => {
    setInputValue(value);
    setError(null);
    
    // Clear selected tag if user modifies input
    if (selectedTag && value !== selectedTag.tagNumber) {
      onClear?.();
    }
  };

  const handleTagSelect = async (tag: EquipmentTag) => {
    setInputValue(tag.tagNumber);
    setShowDropdown(false);
    setSearchResults([]);
    setHighlightedIndex(-1);
    onTagSelect(tag);
  };

  const handleEnterKey = async () => {
    if (selectedTag || !inputValue.trim()) return;

    // If there's a highlighted result, select it
    if (highlightedIndex >= 0 && searchResults[highlightedIndex]) {
      handleTagSelect(searchResults[highlightedIndex]);
      return;
    }

    // Otherwise, try to find exact match
    setIsValidating(true);
    setError(null);

    try {
      const exactMatch = await EquipmentTagService.getEquipmentTagByTagNumber(inputValue.trim());
      
      if (exactMatch) {
        handleTagSelect(exactMatch);
      } else {
        setError('Equipment tag not found');
        onError?.('Equipment tag not found');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to validate tag';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsValidating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleEnterKey();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setHighlightedIndex(prev => 
        prev < searchResults.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setHighlightedIndex(prev => 
        prev > 0 ? prev - 1 : searchResults.length - 1
      );
    } else if (e.key === 'Escape') {
      setShowDropdown(false);
      setHighlightedIndex(-1);
    }
  };

  const handleClear = () => {
    setInputValue("");
    setSearchResults([]);
    setShowDropdown(false);
    setError(null);
    setHighlightedIndex(-1);
    onClear?.();
    inputRef.current?.focus();
  };

  const getStatusIcon = () => {
    if (isValidating || isSearching) {
      return <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />;
    }
    if (selectedTag) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    if (error) {
      return <AlertTriangle className="h-4 w-4 text-red-600" />;
    }
    return <Search className="h-4 w-4 text-muted-foreground" />;
  };

  return (
    <div className="relative w-full">
      {/* Main Input */}
      <div className="relative">
        <Input
          ref={inputRef}
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className={`pr-20 ${error ? 'border-red-500' : selectedTag ? 'border-green-500' : ''}`}
        />
        
        {/* Status Icon */}
        <div className="absolute right-10 top-1/2 transform -translate-y-1/2">
          {getStatusIcon()}
        </div>

        {/* Clear Button */}
        {(inputValue || selectedTag) && !disabled && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
            onClick={handleClear}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="mt-1 text-sm text-red-600 flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          {error}
        </div>
      )}

      {/* Selected Tag Display */}
      {selectedTag && (
        <Card className="mt-2 border-green-200 bg-green-50">
          <CardContent className="p-3">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="font-mono text-green-700 border-green-300">
                    {selectedTag.tagNumber}
                  </Badge>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                
                <div className="text-sm space-y-1">
                  <div className="flex items-center gap-2 text-green-800">
                    <Package className="h-3 w-3" />
                    <span className="font-medium">{selectedTag.equipmentName}</span>
                  </div>
                  <div className="flex items-center gap-2 text-green-700">
                    <Building className="h-3 w-3" />
                    <span>{selectedTag.clientName} - {selectedTag.building}</span>
                  </div>
                  <div className="flex items-center gap-2 text-green-700">
                    <MapPin className="h-3 w-3" />
                    <span>{selectedTag.location}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Results Dropdown */}
      {showDropdown && searchResults.length > 0 && (
        <Card 
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 z-50 mt-1 max-h-64 overflow-y-auto border shadow-lg"
        >
          <CardContent className="p-0">
            {searchResults.map((tag, index) => (
              <div
                key={tag.id}
                className={`p-3 cursor-pointer border-b last:border-b-0 hover:bg-muted ${
                  index === highlightedIndex ? 'bg-muted' : ''
                }`}
                onClick={() => handleTagSelect(tag)}
              >
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="font-mono">
                        {tag.tagNumber}
                      </Badge>
                    </div>
                    
                    <div className="text-sm space-y-1">
                      <div className="flex items-center gap-2">
                        <Package className="h-3 w-3 text-muted-foreground" />
                        <span className="font-medium">{tag.equipmentName}</span>
                      </div>
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Building className="h-3 w-3" />
                        <span className="text-xs">{tag.clientName} - {tag.building}</span>
                      </div>
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <MapPin className="h-3 w-3" />
                        <span className="text-xs">{tag.location}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* No Results Message */}
      {showDropdown && searchResults.length === 0 && inputValue.trim() && !isSearching && (
        <Card className="absolute top-full left-0 right-0 z-50 mt-1 border shadow-lg">
          <CardContent className="p-3 text-center text-muted-foreground">
            <TagIcon className="h-6 w-6 mx-auto mb-2" />
            <p className="text-sm">No equipment tags found</p>
            <p className="text-xs">Try adjusting your search term</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 