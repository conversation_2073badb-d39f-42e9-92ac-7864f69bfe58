"use client";

import { useState } from "react";
import { EquipmentTag } from "@/types/equipment-tag";
import { TagInput } from "./tag-input";
import { QRScanner } from "./qr-scanner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  QrCode, 
  Keyboard, 
  AlertTriangle, 
  CheckCircle,
  Tag as TagIcon,
  Camera,
  Search
} from "lucide-react";

interface TagSelectorProps {
  onTagSelect: (equipmentTag: EquipmentTag) => void;
  selectedTag?: EquipmentTag | null;
  onClear?: () => void;
  disabled?: boolean;
  required?: boolean;
}

export function TagSelector({ 
  onTagSelect, 
  selectedTag = null, 
  onClear, 
  disabled = false,
  required = true
}: TagSelectorProps) {
  const [isQRScannerOpen, setIsQRScannerOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleTagSelect = (tag: EquipmentTag) => {
    setError(null);
    onTagSelect(tag);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleClear = () => {
    setError(null);
    onClear?.();
  };

  const openQRScanner = () => {
    if (disabled) return;
    setError(null);
    setIsQRScannerOpen(true);
  };

  const closeQRScanner = () => {
    setIsQRScannerOpen(false);
  };

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TagIcon className="h-5 w-5" />
            Equipment Selection
            {required && <span className="text-red-500">*</span>}
          </CardTitle>
          <CardDescription>
            {selectedTag 
              ? "Equipment selected. You can change the selection below or proceed with the inspection."
              : "Select equipment by entering the tag number or scanning the QR code to begin inspection."
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Manual Tag Input */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Keyboard className="h-4 w-4" />
              Enter Tag Number
            </div>
            <TagInput
              onTagSelect={handleTagSelect}
              onError={handleError}
              selectedTag={selectedTag}
              onClear={handleClear}
              disabled={disabled}
              placeholder="Type equipment tag number (e.g., JF-001, AHU-B2-01)"
            />
          </div>

          {/* Separator */}
          <div className="flex items-center gap-4">
            <Separator className="flex-1" />
            <span className="text-xs text-muted-foreground">OR</span>
            <Separator className="flex-1" />
          </div>

          {/* QR Scanner Button */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <QrCode className="h-4 w-4" />
              Scan QR Code
            </div>
            <Button
              onClick={openQRScanner}
              disabled={disabled}
              variant="outline"
              className="w-full h-12 border-dashed"
            >
              <Camera className="h-5 w-5 mr-2" />
              Open Camera Scanner
            </Button>
            <p className="text-xs text-muted-foreground text-center">
              Scan the QR code on the equipment tag to automatically load details
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Success State */}
          {selectedTag && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Equipment tag <strong>{selectedTag.tagNumber}</strong> selected successfully! 
                You can now proceed with the inspection.
              </AlertDescription>
            </Alert>
          )}

          {/* Requirement Notice */}
          {required && !selectedTag && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Equipment selection required:</strong> Only registered equipment tags can be inspected. 
                Please select a valid equipment tag to continue.
              </AlertDescription>
            </Alert>
          )}

          {/* Instructions */}
          {!selectedTag && (
            <Card className="bg-muted/30">
              <CardContent className="p-4">
                <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  How to find your equipment tag:
                </h4>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Look for the Auburn Engineering QR code sticker on the equipment</li>
                  <li>• Check the equipment nameplate for the tag number</li>
                  <li>• Tag numbers typically follow patterns like "JF-001", "AHU-B2-01", etc.</li>
                  <li>• If you can't find the tag, contact your supervisor</li>
                </ul>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* QR Scanner Modal */}
      <QRScanner
        isOpen={isQRScannerOpen}
        onClose={closeQRScanner}
        onScanSuccess={handleTagSelect}
        onError={handleError}
      />
    </>
  );
} 