"use client";

import { useAuth } from "@/components/auth/auth-provider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { 
  User, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Info,
  RefreshCw
} from "lucide-react";

export function UserDebug() {
  const { user, extendedUser, loading } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = () => {
    setIsRefreshing(true);
    // Force a page refresh to reload auth state
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  if (loading) {
    return (
      <Card className="mb-4 border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Loading authentication state...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-4 border-blue-200 bg-blue-50">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Info className="h-4 w-4" />
          Authentication Debug Info
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="ml-auto"
          >
            <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Firebase Auth User */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-2">
            <User className="h-4 w-4" />
            Firebase Auth User
          </h4>
          {user ? (
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="font-medium">UID:</span> {user.uid}
              </div>
              <div>
                <span className="font-medium">Email:</span> {user.email || 'N/A'}
              </div>
              <div>
                <span className="font-medium">Name:</span> {user.displayName || 'N/A'}
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">Verified:</span>
                {user.emailVerified ? (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                ) : (
                  <XCircle className="h-3 w-3 text-red-600" />
                )}
              </div>
            </div>
          ) : (
            <Badge variant="destructive">Not authenticated</Badge>
          )}
        </div>

        {/* Extended User (Firestore) */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-2">
            <Shield className="h-4 w-4" />
            Firestore User Document
          </h4>
          {extendedUser ? (
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="font-medium">Role:</span>
                <Badge 
                  className="ml-2"
                  variant={extendedUser.role === 'admin' ? 'default' : 'secondary'}
                >
                  {extendedUser.role}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">Active:</span>
                {extendedUser.isActive ? (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                ) : (
                  <XCircle className="h-3 w-3 text-red-600" />
                )}
              </div>
              <div>
                <span className="font-medium">Created:</span> {new Date(extendedUser.createdAt).toLocaleDateString()}
              </div>
              <div>
                <span className="font-medium">Updated:</span> {new Date(extendedUser.updatedAt).toLocaleDateString()}
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <Badge variant="destructive">No Firestore document found</Badge>
              {user && (
                <p className="text-xs text-muted-foreground">
                  User is authenticated but missing Firestore user document. 
                  This may prevent access to equipment tags.
                </p>
              )}
            </div>
          )}
        </div>

        {/* Permissions Summary */}
        <div>
          <h4 className="font-medium mb-2">Permissions Summary</h4>
          <div className="space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <span>Equipment Tags Read:</span>
              {user ? (
                <CheckCircle className="h-3 w-3 text-green-600" />
              ) : (
                <XCircle className="h-3 w-3 text-red-600" />
              )}
              <span className="text-xs text-muted-foreground">
                {user ? 'Allowed (authenticated)' : 'Denied (not authenticated)'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span>Equipment Tags Write:</span>
              {extendedUser?.role === 'admin' ? (
                <CheckCircle className="h-3 w-3 text-green-600" />
              ) : (
                <XCircle className="h-3 w-3 text-red-600" />
              )}
              <span className="text-xs text-muted-foreground">
                {extendedUser?.role === 'admin' ? 'Allowed (admin)' : 'Denied (not admin)'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 