"use client";

import { useState, useMemo } from "react";
import { EquipmentTag } from "@/types/equipment-tag";
import { EquipmentTagService } from "@/lib/equipment-tag-service";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { 
  Search, 
  MoreVertical, 
  Edit, 
  Trash2, 
  QrCode, 
  Download,
  Printer,
  Calendar,
  Building,
  MapPin,
  Package,
  Tag as TagIcon
} from "lucide-react";

interface EquipmentTagListProps {
  tags: EquipmentTag[];
  onRefresh: () => void;
  onEdit: (tag: EquipmentTag) => void;
  onViewQR: (tag: EquipmentTag) => void;
}

export function EquipmentTagList({ tags, onRefresh, onEdit, onViewQR }: EquipmentTagListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    tag: EquipmentTag | null;
  }>({
    isOpen: false,
    tag: null,
  });
  const [isDeleting, setIsDeleting] = useState(false);

  // Filter tags based on search term
  const filteredTags = useMemo(() => {
    if (!searchTerm) return tags;
    
    const term = searchTerm.toLowerCase();
    return tags.filter(tag => 
      tag.tagNumber.toLowerCase().includes(term) ||
      tag.equipmentName.toLowerCase().includes(term) ||
      tag.clientName.toLowerCase().includes(term) ||
      tag.building.toLowerCase().includes(term) ||
      tag.location.toLowerCase().includes(term)
    );
  }, [tags, searchTerm]);

  const handleDelete = (tag: EquipmentTag) => {
    setDeleteDialog({ isOpen: true, tag });
  };

  const confirmDelete = async () => {
    if (!deleteDialog.tag) return;

    setIsDeleting(true);
    try {
      await EquipmentTagService.deleteEquipmentTag(deleteDialog.tag.id);
      onRefresh(); // Refresh the list
    } catch (error) {
      console.error("Error deleting tag:", error);
      alert("Failed to delete equipment tag. Please try again.");
    } finally {
      setIsDeleting(false);
      setDeleteDialog({ isOpen: false, tag: null });
    }
  };

  const handleDownload = (tag: EquipmentTag) => {
    try {
      EquipmentTagService.downloadQRCode(tag.qrCodeData, tag.tagNumber);
    } catch (error) {
      console.error("Download failed:", error);
      alert("Failed to download QR code. Please try again.");
    }
  };

  const handlePrint = (tag: EquipmentTag) => {
    try {
      EquipmentTagService.printQRCode(tag.qrCodeData, tag);
    } catch (error) {
      console.error("Print failed:", error);
      alert("Failed to print QR code. Please try again.");
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (tags.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <TagIcon className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground mb-2">No Equipment Tags</h3>
          <p className="text-sm text-muted-foreground text-center">
            Create your first equipment tag to get started with QR code management.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="flex items-center gap-2">
              <TagIcon className="h-5 w-5" />
              Equipment Tags ({filteredTags.length})
            </CardTitle>
            
            {/* Search Input */}
            <div className="relative max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">Tag Number</TableHead>
                  <TableHead className="hidden md:table-cell">Equipment</TableHead>
                  <TableHead className="hidden lg:table-cell">Client</TableHead>
                  <TableHead className="hidden sm:table-cell">Location</TableHead>
                  <TableHead className="hidden xl:table-cell">Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTags.map((tag) => (
                  <TableRow key={tag.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <Badge variant="outline" className="font-mono text-xs w-fit">
                          {tag.tagNumber}
                        </Badge>
                        {/* Mobile: Show equipment, client, and location info */}
                        <div className="md:hidden mt-2 space-y-1">
                          <div className="flex items-center gap-2">
                            <Package className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs font-medium">{tag.equipmentName}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Building className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">{tag.clientName}</span>
                          </div>
                          <div className="sm:hidden flex items-center gap-2">
                            <MapPin className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">{tag.location}</span>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{tag.equipmentName}</span>
                      </div>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{tag.clientName}</div>
                          <div className="text-sm text-muted-foreground">{tag.building}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{tag.location}</span>
                      </div>
                    </TableCell>
                    <TableCell className="hidden xl:table-cell">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{formatDate(tag.dateOfCreation)}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem
                            onClick={() => onViewQR(tag)}
                            className="flex items-center gap-2"
                          >
                            <QrCode className="h-4 w-4" />
                            View QR Code
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDownload(tag)}
                            className="flex items-center gap-2"
                          >
                            <Download className="h-4 w-4" />
                            Download QR
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handlePrint(tag)}
                            className="flex items-center gap-2"
                          >
                            <Printer className="h-4 w-4" />
                            Print QR
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onEdit(tag)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit Tag
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDelete(tag)}
                            className="flex items-center gap-2 text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                            Delete Tag
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredTags.length === 0 && searchTerm && (
            <div className="text-center py-8">
              <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">
                No tags found matching &ldquo;{searchTerm}&rdquo;
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog 
        open={deleteDialog.isOpen} 
        onOpenChange={(open) => !open && setDeleteDialog({ isOpen: false, tag: null })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Equipment Tag</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the equipment tag{" "}
              <strong>{deleteDialog.tag?.tagNumber}</strong>? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 