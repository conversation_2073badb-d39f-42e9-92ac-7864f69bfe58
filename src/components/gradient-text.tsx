'use client'

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface GradientTextProps {
  children: React.ReactNode
  className?: string
  gradient?: string
  animate?: boolean
}

export function GradientText({ 
  children, 
  className, 
  gradient = "from-blue-600 via-purple-600 to-indigo-600",
  animate = true 
}: GradientTextProps) {
  return (
    <motion.span
      className={cn(
        `bg-gradient-to-r ${gradient} bg-clip-text text-transparent font-bold`,
        animate && "animate-gradient-x bg-[length:200%_200%]",
        className
      )}
      initial={animate ? { backgroundPosition: "0% 50%" } : undefined}
      animate={animate ? { 
        backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] 
      } : undefined}
      transition={animate ? {
        duration: 5,
        ease: "linear",
        repeat: Infinity,
      } : undefined}
    >
      {children}
    </motion.span>
  )
}

// Keep default export for backward compatibility
export default GradientText; 