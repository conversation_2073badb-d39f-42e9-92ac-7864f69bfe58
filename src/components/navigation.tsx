"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  CheckSquare,
  Database,
  Menu,
  LogIn,
  LayoutDashboard,
  Shield,
} from "lucide-react";
import { GradientText } from "./gradient-text";
import { AuburnIcon } from "./auburn-icon";
import { useAuth, AuthModal, UserProfile } from "./auth";
import { canManageUsers } from "@/types/user";

const navigation = [
  {
    name: "New Checklist",
    href: "/checklist",
    icon: CheckSquare,
    requireAuth: false,
  },
  {
    name: "Saved Inspections",
    href: "/saved",
    icon: Database,
    requireAuth: false,
  },
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    requireAuth: true,
  },
];

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const pathname = usePathname();
  const { user, extendedUser } = useAuth();

  // Filter navigation items based on auth state and add admin link for admins
  const getVisibleNavigation = () => {
    const visibleNav = navigation.filter(item => !item.requireAuth || user);
    
    // Add admin link for users who can manage users
    if (extendedUser && canManageUsers(extendedUser.role)) {
      visibleNav.push({
        name: "Admin",
        href: "/admin",
        icon: Shield,
        requireAuth: true,
      });
    }
    
    return visibleNav;
  };

  const visibleNavigation = getVisibleNavigation();

  return (
    <>
      <motion.header 
        className="sticky top-0 z-50 w-full border-b border-blue-500/20 glass backdrop-blur-xl"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <Link href="/" className="flex items-center space-x-3 group">
                <motion.div
                  whileHover={{ rotate: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  <AuburnIcon size={40} className="drop-shadow-lg" />
                </motion.div>
                <div className="flex flex-col">
                  <span className="font-bold text-lg leading-tight">
                    <GradientText 
                      gradient="from-blue-200 to-cyan-200" 
                      animate={false}
                    >
                      Auburn Engineering
                    </GradientText>
                  </span>
                  <span className="text-xs text-slate-400">PPM Suite</span>
                </div>
              </Link>
            </motion.div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              {visibleNavigation.map((item, index) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;
                
                return (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href={item.href}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                        isActive
                          ? "bg-gradient-to-r from-blue-600 to-teal-600 text-white shadow-2xl shadow-blue-500/25 border border-blue-500/30"
                          : "text-slate-300 hover:text-white hover:bg-blue-900/20 glass"
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{item.name}</span>
                    </Link>
                  </motion.div>
                );
              })}

              {/* Authentication Controls */}
              {user ? (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <UserProfile />
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => setShowAuthModal(true)}
                    className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700 text-white shadow-2xl shadow-blue-500/25"
                  >
                    <LogIn className="h-4 w-4" />
                    <span>Sign In</span>
                  </Button>
                </motion.div>
              )}
            </nav>

            {/* Mobile Menu */}
            <div className="flex md:hidden items-center space-x-2">
              {user ? (
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <UserProfile />
                </motion.div>
              ) : (
                <Button
                  onClick={() => setShowAuthModal(true)}
                  size="sm"
                  className="bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700"
                >
                  <LogIn className="h-4 w-4" />
                </Button>
              )}
              
              <Sheet open={isOpen} onOpenChange={setIsOpen}>
                <SheetTrigger asChild>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Button variant="ghost" size="icon" className="rounded-xl glass hover:bg-blue-900/20 transition-all duration-300">
                      <Menu className="h-5 w-5" />
                      <span className="sr-only">Open menu</span>
                    </Button>
                  </motion.div>
                </SheetTrigger>
                <SheetContent side="right" className="w-[300px] sm:w-[400px] glass border-blue-500/20">
                  <SheetHeader>
                    <SheetTitle className="flex items-center space-x-3">
                      <AuburnIcon size={32} className="drop-shadow-lg" />
                      <GradientText gradient="from-blue-200 to-cyan-200" animate={false}>
                        Auburn Engineering
                      </GradientText>
                    </SheetTitle>
                    <SheetDescription className="text-slate-400">
                      PPM Suite - Professional Inspection Tool
                    </SheetDescription>
                  </SheetHeader>
                  <div className="mt-8 space-y-2">
                    {visibleNavigation.map((item, index) => {
                      const Icon = item.icon;
                      const isActive = pathname === item.href;
                      
                      return (
                        <motion.div
                          key={item.name}
                          initial={{ opacity: 0, x: 50 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          whileHover={{ x: 5 }}
                        >
                          <Link
                            href={item.href}
                            onClick={() => setIsOpen(false)}
                            className={`flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                              isActive
                                ? "bg-gradient-to-r from-blue-600 to-teal-600 text-white shadow-2xl shadow-blue-500/25"
                                : "text-slate-300 hover:text-white hover:bg-blue-900/20 glass"
                            }`}
                          >
                            <Icon className="h-5 w-5" />
                            <span>{item.name}</span>
                          </Link>
                        </motion.div>
                      );
                    })}
                    
                    {!user && (
                      <motion.div
                        initial={{ opacity: 0, x: 50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: 0.3 }}
                        whileHover={{ x: 5 }}
                      >
                        <Button
                          onClick={() => {
                            setShowAuthModal(true);
                            setIsOpen(false);
                          }}
                          className="w-full mt-4 bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700"
                        >
                          <LogIn className="h-4 w-4 mr-2" />
                          Sign In
                        </Button>
                      </motion.div>
                    )}
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </motion.header>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </>
  );
} 