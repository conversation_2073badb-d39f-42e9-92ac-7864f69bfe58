import { Checklist<PERSON>ata, Mechanical<PERSON>heck, ElectricalCheck, SequenceControlsCheck, CheckStatus } from '@/types/checklist';
import { ALL_CHECKS } from '@/config/checklist-fields';

interface PDFTemplateProps {
  checklist: ChecklistData;
}

export function PDFTemplate({ checklist }: PDFTemplateProps) {
  // Calculate summary statistics
  const calculateStats = () => {
    const allItems = [
      ...Object.entries(checklist.mechanicalChecks),
      ...Object.entries(checklist.electricalChecks),
      ...Object.entries(checklist.sequenceControlsChecks)
    ];
    
    const stats = {
      OK: 0,
      Faulty: 0,
      'N/A': 0,
      Missing: 0,
      total: 0
    };
    
    allItems.forEach(([, value]) => {
      if (typeof value === 'string' && (value === 'OK' || value === 'Faulty' || value === 'N/A' || value === 'Missing')) {
        stats[value as CheckStatus]++;
        stats.total++;
      }
    });
    
    return stats;
  };

  const stats = calculateStats();
  const okPercentage = stats.total > 0 ? Math.round((stats.OK / stats.total) * 100) : 0;

  // Create compact pie chart
  const createPieChart = () => {
    if (stats.total === 0) {
      return (
        <div style={{
          width: '80px',
          height: '80px',
          margin: '0 auto 8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '50%',
          backgroundColor: '#f8f9fa',
          border: '2px solid #dee2e6',
          color: '#6c757d',
          fontSize: '10px',
          fontWeight: 'bold'
        }}>
          No Data
        </div>
      );
    }

    const colors = {
      OK: '#28a745',
      Faulty: '#dc3545',
      'N/A': '#ffc107',
      Missing: '#6c757d'
    };

    const data = [
      { label: 'OK', value: stats.OK, color: colors.OK },
      { label: 'Faulty', value: stats.Faulty, color: colors.Faulty },
      { label: 'N/A', value: stats['N/A'], color: colors['N/A'] },
      { label: 'Missing', value: stats.Missing, color: colors.Missing }
    ].filter(item => item.value > 0);

    if (data.length === 1) {
      return (
        <div style={{ margin: '0 auto 8px', width: '80px', height: '80px', display: 'flex', justifyContent: 'center' }}>
          <svg width="80" height="80" viewBox="0 0 80 80">
            <circle
              cx="40"
              cy="40"
              r="35"
              fill={data[0].color}
              stroke="#fff"
              strokeWidth="2"
            />
            <circle
              cx="40"
              cy="40"
              r="18"
              fill="white"
              stroke="#dee2e6"
              strokeWidth="1"
            />
            <text
              x="40"
              y="38"
              textAnchor="middle"
              fontSize="12"
              fontWeight="bold"
              fill="#000"
            >
              100%
            </text>
            <text
              x="40"
              y="48"
              textAnchor="middle"
              fontSize="7"
              fill="#666"
            >
              {data[0].label}
            </text>
          </svg>
        </div>
      );
    }

    const radius = 35;
    const centerX = 40;
    const centerY = 40;
    let currentAngle = -Math.PI / 2;

    const slices = data.map(item => {
      const sliceAngle = (item.value / stats.total) * 2 * Math.PI;
      const startAngle = currentAngle;
      const endAngle = currentAngle + sliceAngle;
      
      const x1 = centerX + radius * Math.cos(startAngle);
      const y1 = centerY + radius * Math.sin(startAngle);
      const x2 = centerX + radius * Math.cos(endAngle);
      const y2 = centerY + radius * Math.sin(endAngle);
      
      const largeArcFlag = sliceAngle > Math.PI ? 1 : 0;
      
      const pathData = [
        `M ${centerX} ${centerY}`,
        `L ${x1} ${y1}`,
        `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        'Z'
      ].join(' ');
      
      currentAngle = endAngle;
      
      return {
        pathData,
        color: item.color,
        percentage: Math.round((item.value / stats.total) * 100)
      };
    });

    return (
      <div style={{ margin: '0 auto 8px', width: '80px', height: '80px', display: 'flex', justifyContent: 'center' }}>
        <svg width="80" height="80" viewBox="0 0 80 80">
          {slices.map((slice, index) => (
            <path
              key={index}
              d={slice.pathData}
              fill={slice.color}
              stroke="#fff"
              strokeWidth="2"
            />
          ))}
          <circle
            cx={centerX}
            cy={centerY}
            r="18"
            fill="white"
            stroke="#dee2e6"
            strokeWidth="1"
          />
          <text
            x={centerX}
            y={centerY - 3}
            textAnchor="middle"
            fontSize="12"
            fontWeight="bold"
            fill="#000"
          >
            {okPercentage}%
          </text>
          <text
            x={centerX}
            y={centerY + 8}
            textAnchor="middle"
            fontSize="7"
            fill="#666"
          >
            OK
          </text>
        </svg>
      </div>
    );
  };

  // Create a mapping from field keys to proper labels
  const getFieldLabel = (fieldKey: string): string => {
    const field = ALL_CHECKS.find(check => check.key === fieldKey);
    return field ? field.label : fieldKey;
  };

  const renderChecklistSection = (title: string, data: MechanicalCheck | ElectricalCheck | SequenceControlsCheck) => {
    const items = Object.entries(data).filter(([, value]) => 
      value !== undefined && value !== null && value !== ''
    );

    const getDisplayValue = (value: string | number | boolean) => {
      if (typeof value === 'string' && (value === 'OK' || value === 'Faulty' || value === 'N/A' || value === 'Missing')) {
        return value;
      }
      return String(value);
    };

    const getStatusColor = (value: string | number | boolean) => {
      if (typeof value === 'string') {
        switch (value) {
          case 'OK': return { color: '#28a745', bg: '#e8f5e8', border: '#28a745' };
          case 'Faulty': return { color: '#dc3545', bg: '#fdeaea', border: '#dc3545' };
          case 'N/A': return { color: '#856404', bg: '#fff3cd', border: '#ffc107' };
          case 'Missing': return { color: '#6c757d', bg: '#f8f9fa', border: '#6c757d' };
        }
      }
      // For numerical or other values
      return { color: '#0066cc', bg: '#e6f3ff', border: '#0066cc' };
    };

    return (
      <div style={{
        backgroundColor: 'white',
        border: '1px solid #e9ecef',
        borderRadius: '6px',
        overflow: 'hidden',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}>
        <div style={{
          background: 'linear-gradient(135deg, #000000 0%, #333333 100%)',
          color: 'white',
          padding: '8px 10px',
          fontSize: '11px',
          fontWeight: '600',
          textAlign: 'center',
          letterSpacing: '0.3px'
        }}>
          {title}
        </div>
        <div style={{
          padding: '8px',
          maxHeight: '680px',
          overflowY: 'auto'
        }}>
          {items.map(([fieldKey, value], index) => {
            const displayValue = getDisplayValue(value);
            const statusStyle = getStatusColor(value);
            
            return (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '4px 0',
                fontSize: '8.5px',
                borderBottom: index < items.length - 1 ? '1px solid #f1f3f4' : 'none',
                minHeight: '22px'
              }}>
                <span style={{ 
                  flex: 1, 
                  lineHeight: '1.3',
                  wordWrap: 'break-word',
                  overflowWrap: 'break-word',
                  hyphens: 'auto',
                  color: '#212529',
                  fontWeight: '500',
                  marginRight: '8px'
                }}>
                  {getFieldLabel(fieldKey)}
                </span>
                <span style={{
                  color: statusStyle.color,
                  fontWeight: '600',
                  fontSize: '7.5px',
                  flexShrink: 0
                }}>
                  {displayValue}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <>
      <style dangerouslySetInnerHTML={{
        __html: `
          @media print {
            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .pdf-container {
              page-break-inside: avoid !important;
              break-inside: avoid !important;
            }
          }
          @page {
            size: A4;
            margin: 0 12mm 12mm 12mm;
          }
        `
      }} />

      <div className="pdf-container" style={{
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        fontSize: '10px',
        lineHeight: '1.3',
        color: '#212529',
        backgroundColor: '#fff',
        width: '210mm',
        height: '297mm',
        margin: '0 auto',
        padding: '5mm 10mm 10mm 10mm',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Modern Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '8px',
          padding: '6px 0',
          borderBottom: '2px solid #000000',
          background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)'
        }}>
          <div>
            <h1 style={{
              fontSize: '16px',
              fontWeight: '700',
              color: '#000000',
              margin: '0 0 2px 0',
              letterSpacing: '-0.3px'
            }}>
              PPM Ventilation System
            </h1>
            <p style={{
              fontSize: '10px',
              color: '#495057',
              margin: '0',
              fontWeight: '500'
            }}>
              Preventive Maintenance Checklist Report
            </p>
          </div>
          <div style={{
            width: '160px',
            height: '80px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
            borderRadius: '4px',
            backgroundColor: 'white',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <img 
              src="/logo.jpeg" 
              alt="Auburn Engineering"
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
            />
          </div>
        </div>

        {/* Compact Information Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(3, 1fr)',
          gap: '8px',
          marginBottom: '10px',
          fontSize: '9px'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
            padding: '8px',
            borderRadius: '4px',
            border: '1px solid #e9ecef'
          }}>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '600', color: '#495057', minWidth: '55px' }}>Client:</span>
              <span style={{ color: '#212529' }}>{checklist.generalInfo.clientName}</span>
            </div>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '600', color: '#495057', minWidth: '55px' }}>Building:</span>
              <span style={{ color: '#212529' }}>{checklist.generalInfo.building}</span>
            </div>
            <div style={{ display: 'flex' }}>
              <span style={{ fontWeight: '600', color: '#495057', minWidth: '55px' }}>Inspector:</span>
              <span style={{ color: '#212529' }}>{checklist.generalInfo.inspectedBy}</span>
            </div>
          </div>
          <div style={{
            background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
            padding: '8px',
            borderRadius: '4px',
            border: '1px solid #e9ecef'
          }}>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '600', color: '#495057', minWidth: '55px' }}>Equipment:</span>
              <span style={{ color: '#212529' }}>{checklist.generalInfo.equipmentName}</span>
            </div>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '600', color: '#495057', minWidth: '55px' }}>Location:</span>
              <span style={{ color: '#212529' }}>{checklist.generalInfo.location}</span>
            </div>
            <div style={{ display: 'flex' }}>
              <span style={{ fontWeight: '600', color: '#495057', minWidth: '55px' }}>Tag No:</span>
              <span style={{ color: '#212529' }}>{checklist.generalInfo.tagNo}</span>
            </div>
          </div>
          <div style={{
            background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
            padding: '8px',
            borderRadius: '4px',
            border: '1px solid #e9ecef'
          }}>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '600', color: '#495057', minWidth: '55px' }}>Date:</span>
              <span style={{ color: '#212529' }}>{checklist.generalInfo.date}</span>
            </div>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '600', color: '#495057', minWidth: '55px' }}>PPM:</span>
              <span style={{ color: '#212529' }}>{checklist.generalInfo.ppmAttempt}</span>
            </div>
            <div style={{ display: 'flex' }}>
              <span style={{ fontWeight: '600', color: '#495057', minWidth: '55px' }}>Approved:</span>
              <span style={{ color: '#212529' }}>{checklist.generalInfo.approvedBy}</span>
            </div>
          </div>
        </div>

        {/* Main Content Grid - Checklists Only */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr 1fr',
          gap: '10px',
          marginBottom: '12px'
        }}>
          {/* Checklists */}
          {renderChecklistSection('Mechanical List', checklist.mechanicalChecks)}
          {renderChecklistSection('Electrical List', checklist.electricalChecks)}
          {renderChecklistSection('Sequence/Controls List', checklist.sequenceControlsChecks)}
        </div>

        {/* Summary Section */}
        <div style={{
          marginBottom: '12px'
        }}>
          <div style={{
            backgroundColor: 'white',
            border: '1px solid #e9ecef',
            borderRadius: '6px',
            overflow: 'hidden',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            width: '100%'
          }}>
            <div style={{
              background: 'linear-gradient(135deg, #000000 0%, #333333 100%)',
              color: 'white',
              padding: '8px 10px',
              fontSize: '11px',
              fontWeight: '600',
              textAlign: 'center',
              letterSpacing: '0.3px'
            }}>
              Inspection Summary & Analysis
            </div>
            <div style={{
              padding: '12px',
              display: 'grid',
              gridTemplateColumns: '120px 1fr 1fr 1fr',
              gap: '20px',
              alignItems: 'flex-start'
            }}>
              {/* Pie Chart */}
              <div style={{ textAlign: 'center' }}>
                {createPieChart()}
                <div style={{
                  fontSize: '7px',
                  color: '#6c757d',
                  marginTop: '4px'
                }}>
                  Overall Status
                </div>
              </div>
              
              {/* Status Legend */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '6px'
              }}>
                <div style={{ fontSize: '9px', fontWeight: '600', color: '#495057', marginBottom: '4px' }}>
                  Status Breakdown
                </div>
                <div style={{ display: 'flex', alignItems: 'center', fontSize: '8px' }}>
                  <div style={{ width: '10px', height: '10px', backgroundColor: '#28a745', marginRight: '6px', borderRadius: '2px' }}></div>
                  <span style={{ flex: 1 }}>Passed:</span>
                  <span style={{ fontWeight: '600', color: '#28a745' }}>{stats.OK}</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', fontSize: '8px' }}>
                  <div style={{ width: '10px', height: '10px', backgroundColor: '#dc3545', marginRight: '6px', borderRadius: '2px' }}></div>
                  <span style={{ flex: 1 }}>Failed:</span>
                  <span style={{ fontWeight: '600', color: '#dc3545' }}>{stats.Faulty}</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', fontSize: '8px' }}>
                  <div style={{ width: '10px', height: '10px', backgroundColor: '#ffc107', marginRight: '6px', borderRadius: '2px' }}></div>
                  <span style={{ flex: 1 }}>N/A:</span>
                  <span style={{ fontWeight: '600', color: '#856404' }}>{stats['N/A']}</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', fontSize: '8px' }}>
                  <div style={{ width: '10px', height: '10px', backgroundColor: '#6c757d', marginRight: '6px', borderRadius: '2px' }}></div>
                  <span style={{ flex: 1 }}>Missing:</span>
                  <span style={{ fontWeight: '600', color: '#6c757d' }}>{stats.Missing}</span>
                </div>
              </div>

              {/* Key Metrics */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '6px'
              }}>
                <div style={{ fontSize: '9px', fontWeight: '600', color: '#495057', marginBottom: '4px' }}>
                  Key Metrics
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '8px' }}>
                  <span>Total Tests:</span>
                  <span style={{ fontWeight: '600' }}>{stats.total}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '8px' }}>
                  <span>Success Rate:</span>
                  <span style={{ fontWeight: '600', color: okPercentage >= 90 ? '#28a745' : okPercentage >= 70 ? '#ffc107' : '#dc3545' }}>
                    {okPercentage}%
                  </span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '8px' }}>
                  <span>Completion:</span>
                  <span style={{ fontWeight: '600' }}>
                    {Math.round(((stats.total - stats.Missing) / stats.total) * 100)}%
                  </span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '8px' }}>
                  <span>Critical Issues:</span>
                  <span style={{ fontWeight: '600', color: stats.Faulty > 0 ? '#dc3545' : '#28a745' }}>
                    {stats.Faulty > 0 ? `${stats.Faulty} Found` : 'None'}
                  </span>
                </div>
              </div>

              {/* Status Assessment */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '6px'
              }}>
                <div style={{ fontSize: '9px', fontWeight: '600', color: '#495057', marginBottom: '4px' }}>
                  Assessment
                </div>
                <div style={{
                  padding: '8px',
                  backgroundColor: okPercentage >= 90 ? '#e8f5e8' : okPercentage >= 70 ? '#fff3cd' : '#fdeaea',
                  borderRadius: '4px',
                  border: `1px solid ${okPercentage >= 90 ? '#28a745' : okPercentage >= 70 ? '#ffc107' : '#dc3545'}`
                }}>
                  <div style={{ 
                    fontSize: '16px', 
                    fontWeight: '700',
                    color: okPercentage >= 90 ? '#28a745' : okPercentage >= 70 ? '#856404' : '#dc3545',
                    textAlign: 'center'
                  }}>
                    {okPercentage}%
                  </div>
                  <div style={{ 
                    fontSize: '7px', 
                    color: '#495057',
                    textAlign: 'center',
                    marginTop: '3px',
                    lineHeight: '1.3'
                  }}>
                    {okPercentage >= 90 
                      ? 'System performing optimally' 
                      : okPercentage >= 70 
                        ? 'Minor issues detected' 
                        : 'Immediate action required'
                    }
                  </div>
                </div>
                <div style={{
                  fontSize: '7px',
                  color: '#6c757d',
                  textAlign: 'center',
                  marginTop: '2px'
                }}>
                  Inspected: {checklist.generalInfo.date}
                </div>
                <div style={{
                  fontSize: '7px',
                  color: '#6c757d',
                  textAlign: 'center'
                }}>
                  Inspector: {checklist.generalInfo.inspectedBy}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Before and After Images Section */}
        <div style={{
          marginBottom: '10px'
        }}>
          <h3 style={{
            fontSize: '12px',
            fontWeight: '600',
            margin: '0 0 6px 0',
            color: '#000000',
            textAlign: 'center'
          }}>
            Before & After Images
          </h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '10px'
          }}>
            {/* Before Image */}
            <div>
              <h4 style={{
                fontSize: '10px',
                fontWeight: '600',
                margin: '0 0 4px 0',
                color: '#495057',
                textAlign: 'center'
              }}>
                Before
              </h4>
              <div style={{
                border: '1px solid #e9ecef',
                borderRadius: '4px',
                height: '100px',
                backgroundColor: '#f8f9fa',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '9px',
                color: '#6c757d',
                overflow: 'hidden'
              }}>
                {checklist.beforeImage ? (
                  <img 
                    src={checklist.beforeImage} 
                    alt="Before"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                ) : (
                  'No image available'
                )}
              </div>
            </div>

            {/* After Image */}
            <div>
              <h4 style={{
                fontSize: '10px',
                fontWeight: '600',
                margin: '0 0 4px 0',
                color: '#495057',
                textAlign: 'center'
              }}>
                After
              </h4>
              <div style={{
                border: '1px solid #e9ecef',
                borderRadius: '4px',
                height: '100px',
                backgroundColor: '#f8f9fa',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '9px',
                color: '#6c757d',
                overflow: 'hidden'
              }}>
                {checklist.afterImage ? (
                  <img 
                    src={checklist.afterImage} 
                    alt="After"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  />
                ) : (
                  'No image available'
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Remarks and Signature Section */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          gap: '10px',
          marginTop: 'auto'
        }}>
          {/* Remarks */}
          <div style={{
            padding: '8px 0'
          }}>
            <h4 style={{
              fontSize: '10px',
              fontWeight: '600',
              margin: '0 0 6px 0',
              color: '#000000'
            }}>
              Remarks
            </h4>
            <div style={{
              fontSize: '9px',
              color: '#495057',
              lineHeight: '1.4',
              minHeight: '35px'
            }}>
              {checklist.remarks || 'No remarks provided'}
            </div>
          </div>

          {/* Signature */}
          <div>
            <h4 style={{
              fontSize: '10px',
              fontWeight: '600',
              margin: '0 0 6px 0',
              color: '#495057',
              textAlign: 'center'
            }}>
              Engineer Signature
            </h4>
            <div style={{
              border: '1px solid #e9ecef',
              borderRadius: '4px',
              height: '60px',
              backgroundColor: '#f8f9fa',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '9px',
              color: '#6c757d',
              overflow: 'hidden'
            }}>
              {checklist.inspectorSignature ? (
                <img 
                  src={checklist.inspectorSignature} 
                  alt="Signature"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain'
                  }}
                />
              ) : (
                'Signature pending'
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 