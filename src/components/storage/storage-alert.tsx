'use client';

import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { X, AlertTriangle, HardDrive, CloudUpload, Trash2 } from 'lucide-react';
import { StorageService } from '@/lib/storage-adapter';
import { FirestoreStorageService } from '@/lib/firestore-storage';
import { MigrationService } from '@/lib/migration-service';
import { useAuth } from '@/components/auth';

interface StorageAlert {
  id: string;
  type: 'quota-exceeded' | 'warning' | 'critical' | 'migration';
  message: string;
  timestamp: number;
  dismissed?: boolean;
}

export function StorageAlert() {
  const { user } = useAuth();
  const [alerts, setAlerts] = useState<StorageAlert[]>([]);
  const [isFirestoreEnabled, setIsFirestoreEnabled] = useState(false);

  useEffect(() => {
    // Check Firestore status
    setIsFirestoreEnabled(MigrationService.isFirestorePersistenceEnabled());
    
    // Check initial storage status
    checkStorageStatus();

    // Check if migration is available but not completed
    if (!MigrationService.isFirestorePersistenceEnabled() && MigrationService.isMigrationNeeded()) {
      addAlert({
        type: 'migration',
        message: 'Firestore persistence is available! Upgrade for 25x more storage and real-time sync.',
      });
    }

    // Listen for storage events
    const handleStorageWarning = (event: CustomEvent) => {
      const { message, usagePercentage } = event.detail;
      addAlert({
        type: usagePercentage > 90 ? 'critical' : 'warning',
        message: message || `Storage usage is ${usagePercentage.toFixed(1)}% full.`,
      });
    };

    const handleQuotaExceeded = (event: CustomEvent) => {
      addAlert({
        type: 'quota-exceeded',
        message: event.detail.message || 'Storage quota exceeded. Manual cleanup required.',
      });
    };

    const handleStorageUpdated = () => {
      checkStorageStatus();
    };

    window.addEventListener('storageWarning', handleStorageWarning as EventListener);
    window.addEventListener('storageQuotaExceeded', handleQuotaExceeded as EventListener);
    window.addEventListener('checklistsUpdated', handleStorageUpdated);

    // Check storage status periodically
    const interval = setInterval(checkStorageStatus, 30000); // Every 30 seconds

    return () => {
      window.removeEventListener('storageWarning', handleStorageWarning as EventListener);
      window.removeEventListener('storageQuotaExceeded', handleQuotaExceeded as EventListener);
      window.removeEventListener('checklistsUpdated', handleStorageUpdated);
      clearInterval(interval);
    };
  }, []);

  const checkStorageStatus = async () => {
    try {
      const storageInfo = await StorageService.getStorageInfo();
      const existingAlert = alerts.find(a => (a.type === 'warning' || a.type === 'critical') && !a.dismissed);

      // Only show storage warnings for legacy localStorage mode
      if (!isFirestoreEnabled) {
    if (storageInfo.usagePercentage > 90 && !existingAlert) {
      addAlert({
        type: 'critical',
            message: `Storage critically full (${storageInfo.usagePercentage.toFixed(1)}% used). Please clean up data or migrate to Firestore.`,
      });
    } else if (storageInfo.usagePercentage > 80 && !existingAlert) {
      addAlert({
        type: 'warning',
            message: `Storage usage high (${storageInfo.usagePercentage.toFixed(1)}% used). Consider migrating to Firestore for 25x more space.`,
      });
    } else if (storageInfo.usagePercentage < 80 && existingAlert) {
      // Remove warning/critical alerts if storage is back to normal
          setAlerts(prev => prev.filter(a => !['warning', 'critical'].includes(a.type) || a.dismissed));
        }
      } else {
        // For Firestore mode, remove any legacy storage warnings
        setAlerts(prev => prev.filter(a => !['warning', 'critical', 'quota-exceeded'].includes(a.type)));
      }
    } catch (error) {
      console.error('Failed to check storage status:', error);
    }
  };

  const addAlert = (alertData: Omit<StorageAlert, 'id' | 'timestamp'>) => {
    const newAlert: StorageAlert = {
      ...alertData,
      id: `${alertData.type}-${Date.now()}`,
      timestamp: Date.now(),
    };

    setAlerts(prev => {
      // Remove any existing alerts of the same type
      const filtered = prev.filter(a => a.type !== alertData.type);
      return [...filtered, newAlert];
    });
  };

  const dismissAlert = (id: string) => {
    setAlerts(prev => prev.filter(a => a.id !== id));
  };

  const handleQuickCleanup = async () => {
    const confirmed = confirm(
      'This will remove old completed and synced checklists. Incomplete and local-only checklists will be preserved. Continue?'
    );
    
    if (!confirmed) return;

    try {
      const result = await StorageService.performCleanup();
      if (result.cleaned > 0) {
        addAlert({
          type: 'warning',
          message: `Cleanup completed! Removed ${result.cleaned} old checklist(s).`,
        });
        // Remove critical/warning alerts after successful cleanup
        setAlerts(prev => prev.filter(a => !['critical', 'warning', 'quota-exceeded'].includes(a.type)));
      } else {
        addAlert({
          type: 'warning',
          message: 'No old data found to clean up.',
        });
      }
    } catch (error) {
      console.error('Quick cleanup failed:', error);
      addAlert({
        type: 'critical',
        message: 'Cleanup failed. Please try manual cleanup from Storage Manager.',
      });
    }
  };

  const handleUpgradeToFirestore = () => {
    // Trigger migration dialog by dispatching an event
    window.dispatchEvent(new CustomEvent('triggerMigration'));
  };

  const getAlertVariant = (type: StorageAlert['type']) => {
    switch (type) {
      case 'quota-exceeded':
      case 'critical':
        return 'destructive' as const;
      case 'migration':
        return 'default' as const;
      case 'warning':
        return 'default' as const;
      default:
        return 'default' as const;
    }
  };

  const getAlertIcon = (type: StorageAlert['type']) => {
    switch (type) {
      case 'quota-exceeded':
      case 'critical':
        return <AlertTriangle className="h-4 w-4" />;
      case 'migration':
        return <CloudUpload className="h-4 w-4" />;
      case 'warning':
        return <HardDrive className="h-4 w-4" />;
      default:
        return <HardDrive className="h-4 w-4" />;
    }
  };

  const visibleAlerts = alerts.filter(a => !a.dismissed);

  if (visibleAlerts.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-md">
      {visibleAlerts.map((alert) => (
        <Alert
          key={alert.id}
          variant={getAlertVariant(alert.type)}
          className="shadow-lg"
        >
          {getAlertIcon(alert.type)}
          <AlertDescription className="pr-8">
            <div className="space-y-2">
              <p className="text-sm">{alert.message}</p>
              
              {alert.type === 'migration' && (
                <div className="flex gap-2">
                  <Button
                    onClick={handleUpgradeToFirestore}
                    size="sm"
                    variant="outline"
                    className="h-6 text-xs"
                  >
                    <CloudUpload className="h-3 w-3 mr-1" />
                    Upgrade Now
                  </Button>
                  <Button
                    onClick={() => dismissAlert(alert.id)}
                    size="sm"
                    variant="ghost"
                    className="h-6 text-xs"
                  >
                    Maybe Later
                  </Button>
                </div>
              )}
              
              {(alert.type === 'critical' || alert.type === 'quota-exceeded') && !isFirestoreEnabled && (
                <div className="flex gap-2">
                  <Button
                    onClick={handleQuickCleanup}
                    size="sm"
                    variant="outline"
                    className="h-6 text-xs"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Cleanup Data
                  </Button>
                  <Button
                    onClick={handleUpgradeToFirestore}
                    size="sm"
                    variant="default"
                    className="h-6 text-xs"
                  >
                    <CloudUpload className="h-3 w-3 mr-1" />
                    Upgrade to Firestore
                  </Button>
                </div>
              )}
              
              {alert.type === 'warning' && !isFirestoreEnabled && (
                <div className="flex gap-2">
                  <Button
                    onClick={handleQuickCleanup}
                    size="sm"
                    variant="outline"
                    className="h-6 text-xs"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Cleanup Data
                  </Button>
                    <Button
                    onClick={handleUpgradeToFirestore}
                      size="sm"
                      variant="outline"
                      className="h-6 text-xs"
                    >
                      <CloudUpload className="h-3 w-3 mr-1" />
                    Upgrade
                    </Button>
                </div>
              )}
            </div>
          </AlertDescription>
          
          <Button
            onClick={() => dismissAlert(alert.id)}
            size="sm"
            variant="ghost"
            className="absolute top-1 right-1 h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </Alert>
      ))}
    </div>
  );
} 