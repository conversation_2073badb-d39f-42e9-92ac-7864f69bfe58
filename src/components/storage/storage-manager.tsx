'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  HardDrive, 
  Trash2, 
  Settings, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle,
  Info,
  Zap,
  Cloud,
  CloudOff
} from 'lucide-react';
import { StorageService, type StorageSettings } from '@/lib/storage-adapter';
import { MigrationService } from '@/lib/migration-service';
import { FirestoreStorageService } from '@/lib/firestore-storage';
import { useAuth } from '@/components/auth';

// Define StorageInfo interface for compatibility
interface StorageInfo {
  currentSize: number;
  maxSize: number;
  usagePercentage: number;
  itemCount: number;
  canSave: boolean;
}

interface StorageManagerProps {
  onStorageChange?: () => void;
}

export function StorageManager({ onStorageChange }: StorageManagerProps) {
  const { user } = useAuth();
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null);
  const [storageSettings, setStorageSettings] = useState<StorageSettings | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isCleanupRunning, setIsCleanupRunning] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [lastCleanupResult, setLastCleanupResult] = useState<{ cleaned: number; errors: string[] } | null>(null);
  const [tempSettings, setTempSettings] = useState<StorageSettings | null>(null);
  const [isFirestoreEnabled, setIsFirestoreEnabled] = useState(false);
  const [cleanupDialog, setCleanupDialog] = useState<{
    isOpen: boolean;
    type: 'auto' | 'emergency' | 'optimize';
    title: string;
    description: string;
    action: () => Promise<void>;
  }>({
    isOpen: false,
    type: 'auto',
    title: '',
    description: '',
    action: async () => {}
  });

  useEffect(() => {
    // Check if Firestore persistence is enabled
    setIsFirestoreEnabled(MigrationService.isFirestorePersistenceEnabled());
    
    loadStorageInfo();
    loadStorageSettings();
    
    // Listen for storage quota exceeded events (mainly for legacy mode)
    const handleQuotaExceeded = (event: CustomEvent) => {
      console.warn('Storage quota exceeded:', event.detail);
      loadStorageInfo(); // Refresh info
    };

    window.addEventListener('storageQuotaExceeded', handleQuotaExceeded as EventListener);
    
    return () => {
      window.removeEventListener('storageQuotaExceeded', handleQuotaExceeded as EventListener);
    };
  }, []);

  const loadStorageInfo = async () => {
    try {
      const info = await StorageService.getStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Failed to load storage info:', error);
      // Fallback to default values
      const defaultSize = isFirestoreEnabled ? 100 * 1024 * 1024 : 4 * 1024 * 1024; // 100MB for Firestore, 4MB for legacy
      setStorageInfo({
        currentSize: 0,
        maxSize: defaultSize,
        usagePercentage: 0,
        itemCount: 0,
        canSave: true
      });
    }
  };

  const loadStorageSettings = async () => {
    if (!user) return;
    
    try {
      // Load settings based on current storage mode
      if (isFirestoreEnabled) {
        const settings = await FirestoreStorageService.getSettings(user.uid);
        setStorageSettings(settings);
        setTempSettings(settings);
      } else {
        // Use default settings for legacy mode
      const defaultSettings: StorageSettings = {
        autoSync: true,
          cacheSize: 4 * 1024 * 1024, // 4MB for legacy
        compressionEnabled: true,
          showStorageWarnings: true,
          daysToKeepCompleted: 30,
          daysToKeepSynced: 7,
        userId: user.uid
      };
      setStorageSettings(defaultSettings);
      setTempSettings(defaultSettings);
      }
    } catch (error) {
      console.error('Failed to load storage settings:', error);
      // Use minimal fallback settings
      const fallbackSettings: StorageSettings = {
        autoSync: isFirestoreEnabled,
        cacheSize: isFirestoreEnabled ? 100 * 1024 * 1024 : 4 * 1024 * 1024,
        compressionEnabled: true,
        showStorageWarnings: true,
        daysToKeepCompleted: 30,
        daysToKeepSynced: 7,
        userId: user?.uid || ''
      };
      setStorageSettings(fallbackSettings);
      setTempSettings(fallbackSettings);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStorageStatusColor = (percentage: number): string => {
    if (isFirestoreEnabled) {
      // Firestore has much higher limits, so adjust thresholds
      if (percentage < 70) return 'text-green-600';
      if (percentage < 90) return 'text-yellow-600';
      return 'text-red-600';
    }
    // Legacy localStorage thresholds
    if (percentage < 50) return 'text-green-600';
    if (percentage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStorageStatusBadge = (percentage: number) => {
    if (isFirestoreEnabled) {
      if (percentage < 70) {
        return <Badge variant="outline" className="text-green-600 border-green-600">Excellent</Badge>;
      }
      if (percentage < 90) {
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Good</Badge>;
      }
      return <Badge variant="outline" className="text-red-600 border-red-600">Full</Badge>;
    }
    
    // Legacy mode badges
    if (percentage < 50) {
      return <Badge variant="outline" className="text-green-600 border-green-600">Good</Badge>;
    }
    if (percentage < 80) {
      return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Warning</Badge>;
    }
    return <Badge variant="outline" className="text-red-600 border-red-600">Critical</Badge>;
  };

  const showCleanupDialog = (type: 'auto' | 'emergency' | 'optimize') => {
    if (isFirestoreEnabled) {
      // Firestore-specific dialogs
      const dialogs = {
        auto: {
          title: 'Firestore Auto-Cleanup',
          description: 'Firestore automatically manages storage and cleanup. Manual cleanup is rarely needed. This will clear local cache.',
          action: performFirestoreCleanup
        },
        emergency: {
          title: 'Clear Local Data',
          description: 'This will clear all local data. Data in Firestore will remain safe and will be re-downloaded when needed.',
          action: performEmergencyCleanup
        },
        optimize: {
          title: 'Firestore Optimization',
          description: 'Firestore automatically optimizes storage. This will refresh the local cache for better performance.',
          action: performOptimization
        }
      };
      
      setCleanupDialog({
        isOpen: true,
        type,
        ...dialogs[type]
      });
    } else {
      // Legacy localStorage dialogs
    const dialogs = {
      auto: {
        title: 'Confirm Auto Cleanup',
          description: `This will remove old completed checklists (older than ${storageSettings?.daysToKeepCompleted || 30} days) and synced checklists (older than ${storageSettings?.daysToKeepSynced || 7} days). Incomplete and local-only checklists will be preserved.`,
          action: performLegacyCleanup
      },
      emergency: {
        title: 'Confirm Emergency Cleanup',
        description: 'This will remove most data, keeping only the 50 most recent incomplete checklists and local-only items. This action cannot be undone.',
        action: performEmergencyCleanup
      },
      optimize: {
        title: 'Confirm Storage Optimization',
        description: 'This will optimize your data by removing empty fields and compressing the storage structure. No data will be lost.',
        action: performOptimization
      }
    };

    setCleanupDialog({
      isOpen: true,
      type,
      ...dialogs[type]
    });
    }
  };

  const performFirestoreCleanup = async () => {
    setIsCleanupRunning(true);
    try {
      await FirestoreStorageService.clearLocalCache();
      await loadStorageInfo();
      onStorageChange?.();
      setLastCleanupResult({ cleaned: 1, errors: [] });
    } catch (error) {
      console.error('Firestore cleanup failed:', error);
      setLastCleanupResult({ cleaned: 0, errors: [error instanceof Error ? error.message : 'Unknown error'] });
    } finally {
      setIsCleanupRunning(false);
      setCleanupDialog(prev => ({ ...prev, isOpen: false }));
    }
  };

  const performLegacyCleanup = async () => {
    setIsCleanupRunning(true);
    try {
      const result = await StorageService.performCleanup();
      setLastCleanupResult(result);
      await loadStorageInfo();
      onStorageChange?.();
    } catch (error) {
      console.error('Legacy cleanup failed:', error);
      setLastCleanupResult({ cleaned: 0, errors: [error instanceof Error ? error.message : 'Unknown error'] });
    } finally {
      setIsCleanupRunning(false);
      setCleanupDialog(prev => ({ ...prev, isOpen: false }));
    }
  };

  const performEmergencyCleanup = async () => {
    setIsCleanupRunning(true);
    try {
      if (user) {
        await StorageService.clearAllChecklists();
        await loadStorageInfo();
        onStorageChange?.();
        setLastCleanupResult({ cleaned: 1, errors: [] });
      }
    } catch (error) {
      console.error('Emergency cleanup failed:', error);
      setLastCleanupResult({ cleaned: 0, errors: [error instanceof Error ? error.message : 'Unknown error'] });
    } finally {
      setIsCleanupRunning(false);
      setCleanupDialog(prev => ({ ...prev, isOpen: false }));
    }
  };

  const performOptimization = async () => {
    setIsOptimizing(true);
    try {
      if (isFirestoreEnabled) {
        await FirestoreStorageService.refreshCache();
      }
      await loadStorageInfo();
      onStorageChange?.();
      setLastCleanupResult({ cleaned: 0, errors: [] });
    } catch (error) {
      console.error('Optimization failed:', error);
      setLastCleanupResult({ cleaned: 0, errors: [error instanceof Error ? error.message : 'Unknown error'] });
    } finally {
      setIsOptimizing(false);
      setCleanupDialog(prev => ({ ...prev, isOpen: false }));
    }
  };

  const executeCleanupAction = async () => {
    await cleanupDialog.action();
  };

  const handleSettingsSave = async () => {
    if (tempSettings && user) {
      try {
        if (isFirestoreEnabled) {
          await FirestoreStorageService.updateSettings(user.uid, tempSettings);
        }
        setStorageSettings(tempSettings);
        setIsSettingsOpen(false);
      } catch (error) {
        console.error('Failed to save settings:', error);
        alert('Failed to save settings. Please try again.');
      }
    }
  };

  const handleSettingsCancel = () => {
    setTempSettings(storageSettings);
    setIsSettingsOpen(false);
  };

  const handleMigrationUpgrade = () => {
    // Trigger migration dialog
    window.dispatchEvent(new CustomEvent('triggerMigration'));
  };

  if (!storageInfo || !storageSettings) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        Loading storage information...
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Storage Type Badge */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base font-medium">Storage System</CardTitle>
            <div className="flex items-center gap-2">
              {isFirestoreEnabled ? (
                <>
                  <Cloud className="h-4 w-4 text-green-600" />
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    Firestore (100MB)
                  </Badge>
                </>
              ) : (
                <>
                  <CloudOff className="h-4 w-4 text-yellow-600" />
                  <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                    Legacy (4MB)
                  </Badge>
                </>
              )}
            </div>
          </div>
          {!isFirestoreEnabled && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Upgrade to Firestore for 25x more storage and real-time sync!</span>
                <Button onClick={handleMigrationUpgrade} size="sm" variant="outline">
                  Upgrade Now
                </Button>
              </AlertDescription>
            </Alert>
          )}
        </CardHeader>
      </Card>

      {/* Storage Overview */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-base font-medium">
            {isFirestoreEnabled ? 'Cache Usage' : 'Local Storage Usage'}
          </CardTitle>
          {getStorageStatusBadge(storageInfo.usagePercentage)}
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span>Used: {formatBytes(storageInfo.currentSize)}</span>
              <span>Max: {formatBytes(storageInfo.maxSize)}</span>
            </div>
            <Progress 
              value={storageInfo.usagePercentage} 
              className="h-2"
            />
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span className={getStorageStatusColor(storageInfo.usagePercentage)}>
                {storageInfo.usagePercentage.toFixed(1)}% used
              </span>
              <span>{storageInfo.itemCount} checklist(s)</span>
            </div>
            {isFirestoreEnabled && (
              <p className="text-xs text-muted-foreground">
                Firestore automatically manages storage and provides 100MB local cache with unlimited cloud storage.
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Storage Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">Storage Management</CardTitle>
          <CardDescription>
            {isFirestoreEnabled 
              ? 'Manage your Firestore cache and settings'
              : 'Manage your local storage and optimize performance'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <Button
              onClick={() => showCleanupDialog('auto')}
              disabled={isCleanupRunning}
              variant="outline"
              className="flex items-center gap-2"
            >
              {isCleanupRunning ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
              {isFirestoreEnabled ? 'Clear Cache' : 'Cleanup Old Data'}
            </Button>

            <Button
              onClick={() => showCleanupDialog('optimize')}
              disabled={isOptimizing}
              variant="outline"
              className="flex items-center gap-2"
            >
              {isOptimizing ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Zap className="h-4 w-4" />
              )}
              {isFirestoreEnabled ? 'Refresh Cache' : 'Optimize Storage'}
            </Button>

            <Button
              onClick={() => setIsSettingsOpen(true)}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Settings
            </Button>
          </div>

          {/* Storage warnings - only for legacy mode */}
          {!isFirestoreEnabled && storageInfo.usagePercentage > 90 && (
            <div className="pt-3 border-t">
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="flex items-center justify-between">
                  <span>Storage critically full! Manual cleanup required.</span>
                  <Button
                    onClick={() => showCleanupDialog('emergency')}
                    variant="destructive"
                    size="sm"
                  >
                    Emergency Cleanup
                  </Button>
                </AlertDescription>
              </Alert>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Last Cleanup Result */}
      {lastCleanupResult && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base font-medium flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              Last Operation Result
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              {lastCleanupResult.cleaned > 0 
                ? `Removed ${lastCleanupResult.cleaned} item(s)`
                : 'Operation completed successfully'
              }
              {lastCleanupResult.errors.length > 0 && (
                <span className="text-red-600 ml-2">
                  ({lastCleanupResult.errors.length} error(s))
                </span>
              )}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Settings Dialog */}
      <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Storage Settings</DialogTitle>
            <DialogDescription>
              {isFirestoreEnabled 
                ? 'Configure Firestore cache and sync settings'
                : 'Configure automatic cleanup and storage limits'
              }
            </DialogDescription>
          </DialogHeader>

          {tempSettings && (
            <div className="space-y-4 py-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="show-warnings">Show Storage Warnings</Label>
                <Switch
                  id="show-warnings"
                  checked={tempSettings.showStorageWarnings}
                  onCheckedChange={(checked) =>
                    setTempSettings({ ...tempSettings, showStorageWarnings: checked })
                  }
                />
              </div>

              {isFirestoreEnabled && (
                <>
              <Separator />
                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-sync">Auto Sync</Label>
                    <Switch
                      id="auto-sync"
                      checked={tempSettings.autoSync}
                      onCheckedChange={(checked) =>
                        setTempSettings({ ...tempSettings, autoSync: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="compression">Enable Compression</Label>
                    <Switch
                      id="compression"
                      checked={tempSettings.compressionEnabled}
                      onCheckedChange={(checked) =>
                        setTempSettings({ ...tempSettings, compressionEnabled: checked })
                      }
                    />
                  </div>
                </>
              )}

              {!isFirestoreEnabled && (
                <>
                  <Separator />
              <div className="space-y-2">
                <Label htmlFor="days-completed">Days to Keep Completed Checklists</Label>
                <Input
                  id="days-completed"
                  type="number"
                  value={tempSettings.daysToKeepCompleted}
                  onChange={(e) =>
                    setTempSettings({ 
                      ...tempSettings, 
                      daysToKeepCompleted: parseInt(e.target.value) || 30 
                    })
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="days-synced">Days to Keep Synced Checklists</Label>
                <Input
                  id="days-synced"
                  type="number"
                  value={tempSettings.daysToKeepSynced}
                  onChange={(e) =>
                    setTempSettings({ 
                      ...tempSettings, 
                      daysToKeepSynced: parseInt(e.target.value) || 7 
                    })
                  }
                />
              </div>
                </>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={handleSettingsCancel}>
              Cancel
            </Button>
            <Button onClick={handleSettingsSave}>
              Save Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cleanup Confirmation Dialog */}
      <Dialog open={cleanupDialog.isOpen} onOpenChange={(open) => 
        setCleanupDialog(prev => ({ ...prev, isOpen: open }))
      }>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{cleanupDialog.title}</DialogTitle>
            <DialogDescription>
              {cleanupDialog.description}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setCleanupDialog(prev => ({ ...prev, isOpen: false }))}
            >
              Cancel
            </Button>
            <Button 
              onClick={executeCleanupAction}
              variant={cleanupDialog.type === 'emergency' ? 'destructive' : 'default'}
            >
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 