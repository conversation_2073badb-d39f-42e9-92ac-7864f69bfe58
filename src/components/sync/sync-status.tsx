'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { FirestoreStorageService } from '@/lib/firestore-storage';
import { MigrationService } from '@/lib/migration-service';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Wifi, 
  WifiOff, 
  Cloud, 
  CloudOff, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle,
  Clock,
  Info
} from 'lucide-react';

interface SyncStatusProps {
  onSyncTriggered?: () => void;
}

export function SyncStatus({ onSyncTriggered }: SyncStatusProps) {
  const { user } = useAuth();
  const { isOnline, wasOffline, resetOfflineFlag } = useNetworkStatus();
  const [syncStatus, setSyncStatus] = useState<'synced' | 'syncing' | 'offline' | 'error'>('offline');
  const [lastSyncTime, setLastSyncTime] = useState<string | null>(null);
  const [isFirestoreEnabled, setIsFirestoreEnabled] = useState(false);

  useEffect(() => {
    // Check if Firestore persistence is enabled
    setIsFirestoreEnabled(MigrationService.isFirestorePersistenceEnabled());
    
    // Load last sync time from localStorage (for legacy compatibility)
    const lastSync = localStorage.getItem('lastSyncTime');
    if (lastSync) {
      setLastSyncTime(lastSync);
    }

    // Listen for checklist updates
    const handleUpdate = () => {
      if (isFirestoreEnabled) {
        // Firestore handles sync automatically, just update timestamp
        const now = new Date().toISOString();
        setLastSyncTime(now);
        localStorage.setItem('lastSyncTime', now);
        setSyncStatus(isOnline ? 'synced' : 'offline');
        onSyncTriggered?.();
      }
    };

    window.addEventListener('checklistsUpdated', handleUpdate);
    
    return () => {
      window.removeEventListener('checklistsUpdated', handleUpdate);
    };
  }, [isFirestoreEnabled, isOnline, onSyncTriggered]);

  // Update sync status based on network status
  useEffect(() => {
    if (isFirestoreEnabled) {
      setSyncStatus(isOnline ? 'synced' : 'offline');
    }
  }, [isOnline, isFirestoreEnabled]);

  // Auto-sync notification when coming back online
  useEffect(() => {
    if (isOnline && wasOffline && user && isFirestoreEnabled) {
      // Firestore handles reconnection automatically
      const now = new Date().toISOString();
      setLastSyncTime(now);
      localStorage.setItem('lastSyncTime', now);
      setSyncStatus('synced');
      onSyncTriggered?.();
      resetOfflineFlag();
    }
  }, [isOnline, wasOffline, user, isFirestoreEnabled, onSyncTriggered, resetOfflineFlag]);

  const handleRefresh = async () => {
    if (!user || !isFirestoreEnabled) return;

    setSyncStatus('syncing');
    try {
      // Force refresh from Firestore
      await FirestoreStorageService.refreshCache();
      
      const now = new Date().toISOString();
      setLastSyncTime(now);
      localStorage.setItem('lastSyncTime', now);
      setSyncStatus('synced');
      onSyncTriggered?.();
    } catch (error) {
      console.error('Refresh failed:', error);
      setSyncStatus('error');
    }
  };

  const formatLastSyncTime = (time: string | null) => {
    if (!time) return 'Never';
    
    const syncTime = new Date(time);
    const now = new Date();
    const diffMs = now.getTime() - syncTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const getNetworkStatusIcon = () => {
    if (isOnline) {
      return <Wifi className="h-4 w-4 text-green-600" />;
    }
    return <WifiOff className="h-4 w-4 text-red-600" />;
  };

  const getNetworkStatusText = () => {
    return isOnline ? 'Online' : 'Offline';
  };

  const getSyncStatusIcon = () => {
    switch (syncStatus) {
      case 'syncing':
      return <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />;
      case 'error':
      return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'offline':
        return <CloudOff className="h-4 w-4 text-gray-600" />;
      case 'synced':
      default:
    return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
  };

  const getSyncStatusText = () => {
    if (!isFirestoreEnabled) return 'Legacy mode';
    
    switch (syncStatus) {
      case 'syncing': return 'Syncing...';
      case 'error': return 'Sync error';
      case 'offline': return 'Offline';
      case 'synced': return 'Real-time sync';
      default: return 'Unknown';
    }
  };

  if (!user) {
    return null; // Don't show sync status for unauthenticated users
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          <Cloud className="h-4 w-4" />
          Sync Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Network Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getNetworkStatusIcon()}
            <span className="text-sm font-medium">{getNetworkStatusText()}</span>
          </div>
          {!isOnline && (
            <Badge variant="destructive" className="text-xs">
              Offline Mode
            </Badge>
          )}
        </div>

        {/* Sync Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getSyncStatusIcon()}
            <span className="text-sm font-medium">{getSyncStatusText()}</span>
          </div>
          <div className="flex items-center gap-2">
            {isFirestoreEnabled && (
              <Badge variant="secondary" className="text-xs">
                Firestore
              </Badge>
            )}
            <Button
              onClick={handleRefresh}
              disabled={!isOnline || syncStatus === 'syncing' || !isFirestoreEnabled}
              size="sm"
              variant="outline"
              className="h-6 text-xs"
            >
              <RefreshCw className={`h-3 w-3 mr-1 ${syncStatus === 'syncing' ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Last Sync Time */}
        <div className="text-xs text-muted-foreground">
          Last updated: {formatLastSyncTime(lastSyncTime)}
        </div>

        {/* Firestore Benefits Info */}
        {isFirestoreEnabled && isOnline && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Real-time sync enabled. Changes are automatically saved and synced across all devices.
            </AlertDescription>
          </Alert>
        )}

        {/* Legacy Mode Warning */}
        {!isFirestoreEnabled && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Using legacy storage. Consider migrating to Firestore for better performance and real-time sync.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
} 