'use client'

import { Canvas } from '@react-three/fiber'
import { Float, Stars } from '@react-three/drei'
import { useMemo, useRef } from 'react'
import * as THREE from 'three'

function FloatingGeometry({ position }: { position: [number, number, number] }) {
  const meshRef = useRef<THREE.Mesh>(null)
  
  return (
    <Float
      speed={1.5}
      rotationIntensity={0.5}
      floatIntensity={0.5}
    >
      <mesh ref={meshRef} position={position}>
        <icosahedronGeometry args={[0.5, 0]} />
        <meshLambertMaterial 
          color="#007CF0" 
          transparent 
          opacity={0.12}
          wireframe
        />
      </mesh>
    </Float>
  )
}

function ParticleField() {
  const count = 100
  const positions = useMemo(() => {
    const positions = new Float32Array(count * 3)
    for (let i = 0; i < count; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 20
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20
    }
    return positions
  }, [])

  return (
    <points>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          args={[positions, 3]}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.04}
        color="#00C9A7"
        transparent
        opacity={0.5}
        sizeAttenuation
      />
    </points>
  )
}

export default function ThreeBackground() {
  const geometryPositions: [number, number, number][] = [
    [-8, 4, -5],
    [8, -4, -8],
    [-5, -6, -3],
    [6, 6, -6],
    [-3, 2, -4],
    [4, -2, -7],
  ]

  return (
    <div className="fixed inset-0 -z-10">
      <Canvas
        camera={{ position: [0, 0, 10], fov: 60 }}
        gl={{ alpha: true, antialias: true }}
      >
        <ambientLight intensity={0.3} />
        <directionalLight position={[10, 10, 5]} intensity={0.2} />
        
        <Stars
          radius={50}
          depth={50}
          count={400}
          factor={2}
          saturation={0}
          fade
          speed={0.4}
        />
        
        <ParticleField />
        
        {geometryPositions.map((position, index) => (
          <FloatingGeometry key={index} position={position} />
        ))}
      </Canvas>
    </div>
  )
} 