export const AI_CONFIG = {
  // Model Configuration
  model: {
    name: "gemini-2.0-flash",
    temperature: 0.7,
    maxTokens: 2048,
  },

  // Feature Flags
  features: {
    remarkGeneration: true,
    remarkEnhancement: true,
    summaryInsights: true,
    predictiveAnalysis: false, // Future feature
    autoRecommendations: false, // Future feature
  },

  // Generation Options - Optimized for PDF Export
  defaultOptions: {
    tone: 'professional' as const,
    includeRecommendations: true,
    maxLength: 500, // Strict character limit for PDF compatibility
    maxWords: 80, // Approximate word count for detailed remarks
  },

  // Prompts Configuration
  prompts: {
    remarkGeneration: {
      systemPrompt: "You are a professional ACMV (Air Conditioning, Mechanical Ventilation) inspection engineer with extensive experience in building systems maintenance and compliance.",
      contextTemplate: "Based on the inspection data provided, generate comprehensive and professional remarks.",
    },
    enhancement: {
      systemPrompt: "You are an expert technical writer specializing in ACMV inspection reports.",
      contextTemplate: "Enhance the following remarks while maintaining their original intent and technical accuracy.",
    },
    insights: {
      systemPrompt: "You are a senior ACMV systems analyst providing strategic insights.",
      contextTemplate: "Analyze the inspection data and provide actionable insights and recommendations.",
    },
  },

  // Rate Limiting
  rateLimits: {
    requestsPerMinute: 10,
    requestsPerHour: 100,
  },

  // Error Messages
  errorMessages: {
    networkError: "Unable to connect to AI service. Please check your internet connection.",
    rateLimitExceeded: "Too many requests. Please wait a moment before trying again.",
    invalidInput: "Invalid input data. Please check your form and try again.",
    serviceUnavailable: "AI service is temporarily unavailable. Please try again later.",
    generic: "An error occurred while processing your request. Please try again.",
  },

  // Future AI Features Configuration
  futureFeatures: {
    // Predictive maintenance suggestions
    predictiveMaintenance: {
      enabled: false,
      confidence_threshold: 0.8,
      lookAhead_days: 30,
    },
    
    // Automated compliance checking
    complianceCheck: {
      enabled: false,
      standards: ['ASHRAE', 'NFPA', 'Local Building Codes'],
    },
    
    // Smart scheduling
    smartScheduling: {
      enabled: false,
      optimization_factors: ['equipment_age', 'usage_patterns', 'failure_history'],
    },
    
    // Anomaly detection
    anomalyDetection: {
      enabled: false,
      sensitivity: 'medium',
      alert_threshold: 0.7,
    },
  },
} as const;

// Type definitions for AI configuration
export type AITone = 'professional' | 'technical' | 'detailed';
export type AIFeature = keyof typeof AI_CONFIG.features;

// Helper functions
export const isFeatureEnabled = (feature: AIFeature): boolean => {
  return AI_CONFIG.features[feature];
};

export const getErrorMessage = (errorType: keyof typeof AI_CONFIG.errorMessages): string => {
  return AI_CONFIG.errorMessages[errorType];
}; 