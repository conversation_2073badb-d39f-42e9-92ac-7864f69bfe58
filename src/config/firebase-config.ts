import { initializeApp, getApps, getApp, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';
import { getAnalytics, Analytics } from 'firebase/analytics';
import { 
  getFirestore, 
  initializeFirestore, 
  Firestore, 
  persistentLocalCache, 
  persistentMultipleTabManager 
} from 'firebase/firestore';
import { getFunctions, Functions } from 'firebase/functions';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Singleton instances
let firebaseApp: FirebaseApp;
let firebaseAuth: Auth;
let firebaseFirestore: Firestore;
let firebaseFunctions: Functions;
let firebaseAnalytics: Analytics | null = null;

// Initialize Firebase App (singleton)
export function getFirebaseApp(): FirebaseApp {
  if (!firebaseApp) {
    firebaseApp = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();
  }
  return firebaseApp;
}

// Initialize Firebase Auth (singleton)
export function getFirebaseAuth(): Auth {
  if (!firebaseAuth) {
    firebaseAuth = getAuth(getFirebaseApp());
  }
  return firebaseAuth;
}

// Initialize Firestore with persistence (singleton)
export function getFirebaseFirestore(enablePersistence: boolean = false): Firestore {
  if (!firebaseFirestore) {
    const app = getFirebaseApp();
    
    if (enablePersistence) {
      try {
        // Try to initialize with persistence
        firebaseFirestore = initializeFirestore(app, {
          localCache: persistentLocalCache({
            cacheSizeBytes: 100 * 1024 * 1024, // 100MB cache
            tabManager: persistentMultipleTabManager() // Multi-tab support
          })
        });
        console.log('Firestore initialized with persistence');
      } catch (error: any) {
        // If already initialized, get the existing instance
        if (error.code === 'already-initialized' || error.message?.includes('already been called')) {
          console.warn('Firestore already initialized, using existing instance');
          firebaseFirestore = getFirestore(app);
        } else {
          console.error('Failed to initialize Firestore with persistence:', error);
          // Fallback to basic Firestore
          firebaseFirestore = getFirestore(app);
        }
      }
    } else {
      // Basic Firestore without persistence
      firebaseFirestore = getFirestore(app);
    }
  }
  return firebaseFirestore;
}

// Initialize Firebase Functions (singleton)
export function getFirebaseFunctions(): Functions {
  if (!firebaseFunctions) {
    firebaseFunctions = getFunctions(getFirebaseApp());
  }
  return firebaseFunctions;
}

// Initialize Firebase Analytics (singleton, browser only)
export function getFirebaseAnalytics(): Analytics | null {
  if (typeof window !== 'undefined' && !firebaseAnalytics) {
    try {
      firebaseAnalytics = getAnalytics(getFirebaseApp());
    } catch (error) {
      console.warn('Analytics not available:', error);
      firebaseAnalytics = null;
    }
  }
  return firebaseAnalytics;
}

// Check if Firebase services are initialized
export function isFirebaseInitialized(): boolean {
  return getApps().length > 0;
}

// Reset Firebase instances (for testing or reinitialization)
export function resetFirebaseInstances(): void {
  firebaseApp = undefined as any;
  firebaseAuth = undefined as any;
  firebaseFirestore = undefined as any;
  firebaseFunctions = undefined as any;
  firebaseAnalytics = null;
} 