// Firebase configuration with persistence enabled
// This uses the centralized Firebase configuration to avoid initialization conflicts

import { 
  getFirebaseApp, 
  getFirebaseAuth, 
  getFirebaseFirestore, 
  getFirebaseFunctions, 
  getFirebaseAnalytics 
} from './firebase-config';

// Get Firebase services with persistence enabled
export const app = getFirebaseApp();
export const auth = getFirebaseAuth();
export const db = getFirebaseFirestore(true); // Enable persistence
export const functions = getFirebaseFunctions();
export const analytics = getFirebaseAnalytics();

export default app; 