import { useState, useCallback } from 'react';
import { aiService, RemarkGenerationOptions } from '@/lib/ai-service';
import { ChecklistFormData } from '@/lib/validation';

export interface UseAIReturn {
  // State
  isGenerating: boolean;
  error: string | null;
  
  // Actions
  generateRemarks: (
    checklistData: Partial<ChecklistFormData>,
    currentRemarks?: string,
    options?: RemarkGenerationOptions
  ) => Promise<string | null>;
  
  enhanceRemarks: (
    currentRemarks: string,
    checklistData: Partial<ChecklistFormData>
  ) => Promise<string | null>;
  
  generateInsights: (
    checklistData: ChecklistFormData
  ) => Promise<string | null>;
  
  clearError: () => void;
}

export function useAI(): UseAIReturn {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const generateRemarks = useCallback(async (
    checklistData: Partial<ChecklistFormData>,
    currentRemarks: string = '',
    options: RemarkGenerationOptions = {}
  ): Promise<string | null> => {
    setIsGenerating(true);
    setError(null);
    
    try {
      const result = await aiService.generateRemarks(checklistData, currentRemarks, options);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate remarks';
      setError(errorMessage);
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const enhanceRemarks = useCallback(async (
    currentRemarks: string,
    checklistData: Partial<ChecklistFormData>
  ): Promise<string | null> => {
    setIsGenerating(true);
    setError(null);
    
    try {
      const result = await aiService.enhanceRemarks(currentRemarks, checklistData);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to enhance remarks';
      setError(errorMessage);
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const generateInsights = useCallback(async (
    checklistData: ChecklistFormData
  ): Promise<string | null> => {
    setIsGenerating(true);
    setError(null);
    
    try {
      const result = await aiService.generateSummaryInsights(checklistData);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate insights';
      setError(errorMessage);
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  return {
    isGenerating,
    error,
    generateRemarks,
    enhanceRemarks,
    generateInsights,
    clearError
  };
} 