'use client';

import { useState, useEffect } from 'react';

interface NetworkStatus {
  isOnline: boolean;
  wasOffline: boolean;
}

export function useNetworkStatus() {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    wasOffline: false
  });

  useEffect(() => {
    const handleOnline = () => {
      setNetworkStatus(prev => ({
        isOnline: true,
        wasOffline: prev.wasOffline || !prev.isOnline
      }));
    };

    const handleOffline = () => {
      setNetworkStatus(prev => ({
        isOnline: false,
        wasOffline: true
      }));
    };

    // Listen to browser events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check actual connectivity with a lightweight test
    const checkConnectivity = async () => {
      try {
        const response = await fetch('/api/ping', { 
          method: 'HEAD',
          cache: 'no-cache',
          signal: AbortSignal.timeout(5000)
        });
        const isOnline = response.ok;
      setNetworkStatus(prev => ({
        isOnline,
        wasOffline: prev.wasOffline || !isOnline
      }));
      } catch {
        setNetworkStatus(prev => ({
          isOnline: false,
          wasOffline: true
        }));
      }
    };

    // Initial connectivity check
    checkConnectivity();

    // Periodic check every 30 seconds when online
    const interval = setInterval(() => {
      if (navigator.onLine) {
        checkConnectivity();
      }
    }, 30000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, []);

  const resetOfflineFlag = () => {
    setNetworkStatus(prev => ({ ...prev, wasOffline: false }));
  };

  return {
    ...networkStatus,
    resetOfflineFlag
  };
} 