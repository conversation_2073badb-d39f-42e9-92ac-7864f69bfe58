import { 
  collection, 
  getDocs, 
  query, 
  orderBy, 
  doc,
  updateDoc,
  deleteDoc,
  getDoc,
  where,
  Timestamp,
  deleteField
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { ChecklistData, SyncStatus } from '@/types/checklist';
import { EquipmentTag } from '@/types/equipment-tag';
import { StorageService } from './storage';

const CHECKLISTS_COLLECTION = 'checklists';
const EQUIPMENT_TAGS_COLLECTION = 'equipment-tags';

export interface ChecklistWithUser extends ChecklistData {
  userDisplayName?: string;
  userEmail?: string;
  // Equipment tag data for admin queries
  equipmentTag?: EquipmentTag;
}

export interface ChecklistStats {
  total: number;
  completed: number;
  pending: number;
  local: number;
  synced: number;
  conflicts: number;
}

export class AdminChecklistService {
  /**
   * Get all checklists from cloud storage (admin only)
   */
  static async getAllChecklists(): Promise<ChecklistWithUser[]> {
    try {
      const checklistsRef = collection(db, CHECKLISTS_COLLECTION);
      const q = query(checklistsRef, orderBy('createdAt', 'desc'));
      
      const querySnapshot = await getDocs(q);
      const checklists: ChecklistWithUser[] = [];

      for (const docSnapshot of querySnapshot.docs) {
        const data = docSnapshot.data();
        
        // Convert Firestore timestamps
        const createdAt = data.createdAt instanceof Timestamp 
          ? data.createdAt.toDate().toISOString()
          : data.createdAt;
        const updatedAt = data.updatedAt instanceof Timestamp 
          ? data.updatedAt.toDate().toISOString()
          : data.updatedAt;

        // Get user information
        let userDisplayName: string | undefined;
        let userEmail: string | undefined;
        
        if (data.userId) {
          try {
            const userDoc = await getDoc(doc(db, 'users', data.userId));
            if (userDoc.exists()) {
              const userData = userDoc.data();
              userDisplayName = userData.displayName;
              userEmail = userData.email;
            }
          } catch (error) {
            console.warn(`Failed to fetch user data for ${data.userId}:`, error);
          }
        }

        // Get equipment tag information for admin queries
        let equipmentTag: EquipmentTag | undefined;
        if (data.equipmentTagId) {
          try {
            const tagDoc = await getDoc(doc(db, EQUIPMENT_TAGS_COLLECTION, data.equipmentTagId));
            if (tagDoc.exists()) {
              const tagData = tagDoc.data();
              // Convert Firestore timestamps for equipment tag
              const tagCreatedAt = tagData.createdAt instanceof Timestamp 
                ? tagData.createdAt.toDate().toISOString()
                : tagData.createdAt;
              const tagUpdatedAt = tagData.updatedAt instanceof Timestamp 
                ? tagData.updatedAt.toDate().toISOString()
                : tagData.updatedAt;
              
              equipmentTag = {
                ...tagData,
                id: tagDoc.id,
                createdAt: tagCreatedAt,
                updatedAt: tagUpdatedAt,
              } as EquipmentTag;
            }
          } catch (error) {
            console.warn(`Failed to fetch equipment tag data for ${data.equipmentTagId}:`, error);
          }
        }

        checklists.push({
          ...data,
          id: docSnapshot.id,
          createdAt,
          updatedAt,
          userDisplayName,
          userEmail,
          equipmentTag,
          // Ensure completion fields exist
          isCompleted: data.isCompleted || false,
          completedAt: data.completedAt,
          completedBy: data.completedBy,
          syncMetadata: {
            status: 'synced' as SyncStatus,
            lastSyncedAt: new Date().toISOString(),
            lastLocalUpdateAt: updatedAt,
            cloudVersion: data.version || 1,
            localVersion: data.version || 1
          }
        } as ChecklistWithUser);
      }

      return checklists;
    } catch (error) {
      console.error('Error fetching all checklists:', error);
      throw error;
    }
  }

  /**
   * Get a specific checklist by ID (admin only)
   */
  static async getChecklistById(checklistId: string): Promise<ChecklistWithUser | null> {
    try {
      const docRef = doc(db, CHECKLISTS_COLLECTION, checklistId);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        return null;
      }

      const data = docSnap.data();
      
      // Convert Firestore timestamps
      const createdAt = data.createdAt instanceof Timestamp 
        ? data.createdAt.toDate().toISOString()
        : data.createdAt;
      const updatedAt = data.updatedAt instanceof Timestamp 
        ? data.updatedAt.toDate().toISOString()
        : data.updatedAt;

      // Get user information
      let userDisplayName: string | undefined;
      let userEmail: string | undefined;
      
      if (data.userId) {
        try {
          const userDoc = await getDoc(doc(db, 'users', data.userId));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            userDisplayName = userData.displayName;
            userEmail = userData.email;
          }
        } catch (error) {
          console.warn(`Failed to fetch user data for ${data.userId}:`, error);
        }
      }

      // Get equipment tag information for admin queries
      let equipmentTag: EquipmentTag | undefined;
      if (data.equipmentTagId) {
        try {
          const tagDoc = await getDoc(doc(db, EQUIPMENT_TAGS_COLLECTION, data.equipmentTagId));
          if (tagDoc.exists()) {
            const tagData = tagDoc.data();
            // Convert Firestore timestamps for equipment tag
            const tagCreatedAt = tagData.createdAt instanceof Timestamp 
              ? tagData.createdAt.toDate().toISOString()
              : tagData.createdAt;
            const tagUpdatedAt = tagData.updatedAt instanceof Timestamp 
              ? tagData.updatedAt.toDate().toISOString()
              : tagData.updatedAt;
            
            equipmentTag = {
              ...tagData,
              id: tagDoc.id,
              createdAt: tagCreatedAt,
              updatedAt: tagUpdatedAt,
            } as EquipmentTag;
          }
        } catch (error) {
          console.warn(`Failed to fetch equipment tag data for ${data.equipmentTagId}:`, error);
        }
      }

      return {
        ...data,
        id: docSnap.id,
        createdAt,
        updatedAt,
        userDisplayName,
        userEmail,
        equipmentTag,
        isCompleted: data.isCompleted || false,
        completedAt: data.completedAt,
        completedBy: data.completedBy,
        syncMetadata: {
          status: 'synced' as SyncStatus,
          lastSyncedAt: new Date().toISOString(),
          lastLocalUpdateAt: updatedAt,
          cloudVersion: data.version || 1,
          localVersion: data.version || 1
        }
      } as ChecklistWithUser;
    } catch (error) {
      console.error('Error fetching checklist:', error);
      throw error;
    }
  }

  /**
   * Update a checklist (admin only)
   */
  static async updateChecklist(checklistId: string, updates: Partial<ChecklistData>): Promise<boolean> {
    try {
      const docRef = doc(db, CHECKLISTS_COLLECTION, checklistId);
      
      // Prepare update data
      const updateData = {
        ...updates,
        updatedAt: Timestamp.now(),
        version: (updates.syncMetadata?.localVersion || 1) + 1
      };

      // Remove syncMetadata from Firestore update
      const { syncMetadata, ...baseData } = updateData;

      // Filter out undefined values to prevent Firestore errors
      const firestoreData: any = {};
      Object.entries(baseData).forEach(([key, value]) => {
        if (value !== undefined) {
          firestoreData[key] = value;
        }
      });

      await updateDoc(docRef, firestoreData);
      return true;
    } catch (error) {
      console.error('Error updating checklist:', error);
      throw error;
    }
  }

  /**
   * Delete a checklist (admin only)
   */
  static async deleteChecklist(checklistId: string): Promise<boolean> {
    try {
      const docRef = doc(db, CHECKLISTS_COLLECTION, checklistId);
      await deleteDoc(docRef);
      return true;
    } catch (error) {
      console.error('Error deleting checklist:', error);
      throw error;
    }
  }

  /**
   * Bulk delete checklists (admin only)
   */
  static async bulkDeleteChecklists(checklistIds: string[]): Promise<{ success: string[]; failed: string[] }> {
    const results = { success: [] as string[], failed: [] as string[] };
    
    for (const id of checklistIds) {
      try {
        const success = await this.deleteChecklist(id);
        if (success) {
          results.success.push(id);
        } else {
          results.failed.push(id);
        }
      } catch (error) {
        results.failed.push(id);
      }
    }
    
    return results;
  }

  /**
   * Toggle checklist completion status (admin only)
   */
  static async toggleCompletionStatus(
    checklistId: string, 
    completed: boolean, 
    adminUserId: string
  ): Promise<boolean> {
    try {
      const docRef = doc(db, CHECKLISTS_COLLECTION, checklistId);
      
      const updates: any = {
        isCompleted: completed,
        updatedAt: Timestamp.now()
      };

      if (completed) {
        updates.completedAt = new Date().toISOString();
        updates.completedBy = adminUserId;
      } else {
        // Use deleteField to remove the fields instead of setting them to undefined
        updates.completedAt = deleteField();
        updates.completedBy = deleteField();
      }

      await updateDoc(docRef, updates);
      return true;
    } catch (error) {
      console.error('Error toggling completion status:', error);
      throw error;
    }
  }

  /**
   * Get checklist statistics
   */
  static async getChecklistStats(): Promise<ChecklistStats> {
    try {
      const checklists = await this.getAllChecklists();
      
      return {
        total: checklists.length,
        completed: checklists.filter(c => c.isCompleted).length,
        pending: checklists.filter(c => !c.isCompleted).length,
        local: 0, // Cloud checklists are always synced
        synced: checklists.length,
        conflicts: 0 // Admin view shows resolved conflicts
      };
    } catch (error) {
      console.error('Error getting checklist stats:', error);
      throw error;
    }
  }

  /**
   * Search checklists by various criteria
   */
  static async searchChecklists(criteria: {
    userId?: string;
    equipmentTag?: string;
    dateFrom?: string;
    dateTo?: string;
    completed?: boolean;
  }): Promise<ChecklistWithUser[]> {
    try {
      let q = query(collection(db, CHECKLISTS_COLLECTION), orderBy('createdAt', 'desc'));

      // Apply filters
      if (criteria.userId) {
        q = query(q, where('userId', '==', criteria.userId));
      }
      
      if (criteria.completed !== undefined) {
        q = query(q, where('isCompleted', '==', criteria.completed));
      }

      const querySnapshot = await getDocs(q);
      const checklists: ChecklistWithUser[] = [];

      for (const docSnapshot of querySnapshot.docs) {
        const data = docSnapshot.data();
        
        // Convert timestamps
        const createdAt = data.createdAt instanceof Timestamp 
          ? data.createdAt.toDate().toISOString()
          : data.createdAt;
        const updatedAt = data.updatedAt instanceof Timestamp 
          ? data.updatedAt.toDate().toISOString()
          : data.updatedAt;

        // Apply additional filters
        if (criteria.dateFrom && createdAt < criteria.dateFrom) continue;
        if (criteria.dateTo && createdAt > criteria.dateTo) continue;
        if (criteria.equipmentTag && 
            !data.generalInfo?.equipmentTag?.toLowerCase().includes(criteria.equipmentTag.toLowerCase())) {
          continue;
        }

        // Get user information
        let userDisplayName: string | undefined;
        let userEmail: string | undefined;
        
        if (data.userId) {
          try {
            const userDoc = await getDoc(doc(db, 'users', data.userId));
            if (userDoc.exists()) {
              const userData = userDoc.data();
              userDisplayName = userData.displayName;
              userEmail = userData.email;
            }
          } catch (error) {
            console.warn(`Failed to fetch user data for ${data.userId}:`, error);
          }
        }

        // Get equipment tag information
        let equipmentTag: EquipmentTag | undefined;
        if (data.equipmentTagId) {
          try {
            const tagDoc = await getDoc(doc(db, EQUIPMENT_TAGS_COLLECTION, data.equipmentTagId));
            if (tagDoc.exists()) {
              const tagData = tagDoc.data();
              const tagCreatedAt = tagData.createdAt instanceof Timestamp 
                ? tagData.createdAt.toDate().toISOString()
                : tagData.createdAt;
              const tagUpdatedAt = tagData.updatedAt instanceof Timestamp 
                ? tagData.updatedAt.toDate().toISOString()
                : tagData.updatedAt;
              
              equipmentTag = {
                ...tagData,
                id: tagDoc.id,
                createdAt: tagCreatedAt,
                updatedAt: tagUpdatedAt,
              } as EquipmentTag;
            }
          } catch (error) {
            console.warn(`Failed to fetch equipment tag data for ${data.equipmentTagId}:`, error);
          }
        }

        checklists.push({
          ...data,
          id: docSnapshot.id,
          createdAt,
          updatedAt,
          userDisplayName,
          userEmail,
          equipmentTag,
          isCompleted: data.isCompleted || false,
          completedAt: data.completedAt,
          completedBy: data.completedBy,
          syncMetadata: {
            status: 'synced' as SyncStatus,
            lastSyncedAt: new Date().toISOString(),
            lastLocalUpdateAt: updatedAt,
            cloudVersion: data.version || 1,
            localVersion: data.version || 1
          }
        } as ChecklistWithUser);
      }

      return checklists;
    } catch (error) {
      console.error('Error searching checklists:', error);
      throw error;
    }
  }

  /**
   * Get checklists by location (building and/or location)
   */
  static async getChecklistsByLocation(building?: string, location?: string): Promise<ChecklistWithUser[]> {
    try {
      // First get all equipment tags that match the location criteria
      let equipmentTagIds: string[] = [];
      
      if (building || location) {
        let tagQuery = query(collection(db, EQUIPMENT_TAGS_COLLECTION));
        
        if (building) {
          tagQuery = query(tagQuery, where('building', '==', building));
        }
        
        // Note: Firestore doesn't support multiple where clauses on different fields efficiently
        // For location filtering, we'll fetch and filter in memory
        const tagSnapshot = await getDocs(tagQuery);
        
        tagSnapshot.forEach((doc) => {
          const tagData = doc.data();
          if (!location || tagData.location === location) {
            equipmentTagIds.push(doc.id);
          }
        });
      }

      if (equipmentTagIds.length === 0) {
        return [];
      }

      // Now get checklists that reference these equipment tags
      // Note: Firestore 'in' queries are limited to 10 items, so we may need to batch
      const checklists: ChecklistWithUser[] = [];
      const batchSize = 10;
      
      for (let i = 0; i < equipmentTagIds.length; i += batchSize) {
        const batch = equipmentTagIds.slice(i, i + batchSize);
        const checklistQuery = query(
          collection(db, CHECKLISTS_COLLECTION),
          where('equipmentTagId', 'in', batch),
          orderBy('createdAt', 'desc')
        );
        
        const querySnapshot = await getDocs(checklistQuery);
        
        for (const docSnapshot of querySnapshot.docs) {
          const data = docSnapshot.data();
          
          // Convert timestamps
          const createdAt = data.createdAt instanceof Timestamp 
            ? data.createdAt.toDate().toISOString()
            : data.createdAt;
          const updatedAt = data.updatedAt instanceof Timestamp 
            ? data.updatedAt.toDate().toISOString()
            : data.updatedAt;

          // Get user information
          let userDisplayName: string | undefined;
          let userEmail: string | undefined;
          
          if (data.userId) {
            try {
              const userDoc = await getDoc(doc(db, 'users', data.userId));
              if (userDoc.exists()) {
                const userData = userDoc.data();
                userDisplayName = userData.displayName;
                userEmail = userData.email;
              }
            } catch (error) {
              console.warn(`Failed to fetch user data for ${data.userId}:`, error);
            }
          }

          // Get equipment tag information
          let equipmentTag: EquipmentTag | undefined;
          if (data.equipmentTagId) {
            try {
              const tagDoc = await getDoc(doc(db, EQUIPMENT_TAGS_COLLECTION, data.equipmentTagId));
              if (tagDoc.exists()) {
                const tagData = tagDoc.data();
                const tagCreatedAt = tagData.createdAt instanceof Timestamp 
                  ? tagData.createdAt.toDate().toISOString()
                  : tagData.createdAt;
                const tagUpdatedAt = tagData.updatedAt instanceof Timestamp 
                  ? tagData.updatedAt.toDate().toISOString()
                  : tagData.updatedAt;
                
                equipmentTag = {
                  ...tagData,
                  id: tagDoc.id,
                  createdAt: tagCreatedAt,
                  updatedAt: tagUpdatedAt,
                } as EquipmentTag;
              }
            } catch (error) {
              console.warn(`Failed to fetch equipment tag data for ${data.equipmentTagId}:`, error);
            }
          }

          checklists.push({
            ...data,
            id: docSnapshot.id,
            createdAt,
            updatedAt,
            userDisplayName,
            userEmail,
            equipmentTag,
            isCompleted: data.isCompleted || false,
            completedAt: data.completedAt,
            completedBy: data.completedBy,
            syncMetadata: {
              status: 'synced' as SyncStatus,
              lastSyncedAt: new Date().toISOString(),
              lastLocalUpdateAt: updatedAt,
              cloudVersion: data.version || 1,
              localVersion: data.version || 1
            }
          } as ChecklistWithUser);
        }
      }

      // Sort by creation date desc
      checklists.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      
      return checklists;
    } catch (error) {
      console.error('Error getting checklists by location:', error);
      throw error;
    }
  }

  /**
   * Get unique buildings from equipment tags
   */
  static async getUniqueBuildings(): Promise<string[]> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const querySnapshot = await getDocs(tagsRef);
      
      const buildings = new Set<string>();
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.building) {
          buildings.add(data.building);
        }
      });
      
      return Array.from(buildings).sort();
    } catch (error) {
      console.error('Error getting unique buildings:', error);
      throw error;
    }
  }

  /**
   * Get unique locations from equipment tags for a specific building
   */
  static async getUniqueLocations(building?: string): Promise<string[]> {
    try {
      let tagsQuery = query(collection(db, EQUIPMENT_TAGS_COLLECTION));
      
      if (building) {
        tagsQuery = query(tagsQuery, where('building', '==', building));
      }
      
      const querySnapshot = await getDocs(tagsQuery);
      
      const locations = new Set<string>();
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.location) {
          locations.add(data.location);
        }
      });
      
      return Array.from(locations).sort();
    } catch (error) {
      console.error('Error getting unique locations:', error);
      throw error;
    }
  }

  /**
   * Get unique clients from equipment tags
   */
  static async getUniqueClients(): Promise<string[]> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const querySnapshot = await getDocs(tagsRef);
      
      const clients = new Set<string>();
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.clientName) {
          clients.add(data.clientName);
        }
      });
      
      return Array.from(clients).sort();
    } catch (error) {
      console.error('Error getting unique clients:', error);
      throw error;
    }
  }
} 