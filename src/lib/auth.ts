import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile as firebaseUpdateProfile,
  GoogleAuthProvider,
  signInWithPopup,
  onAuthStateChanged,
  User
} from 'firebase/auth';
import { auth } from '@/config/firebase-persistence';
import { AuthUser, convertFirebaseUser } from '@/types/auth';

export class AuthService {
  static async signIn(email: string, password: string): Promise<AuthUser> {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return convertFirebaseUser(userCredential.user);
  }

  static async signUp(email: string, password: string, displayName?: string): Promise<AuthUser> {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    
    if (displayName) {
      await firebaseUpdateProfile(userCredential.user, { displayName });
    }
    
    return convertFirebaseUser(userCredential.user);
  }

  static async signInWithGoogle(): Promise<AuthUser> {
    const provider = new GoogleAuthProvider();
    const userCredential = await signInWithPopup(auth, provider);
    return convertFirebaseUser(userCredential.user);
  }

  static async signOut(): Promise<void> {
    await firebaseSignOut(auth);
  }

  static async resetPassword(email: string): Promise<void> {
    await sendPasswordResetEmail(auth, email);
  }

  static async updateProfile(displayName: string, photoURL?: string): Promise<void> {
    if (!auth.currentUser) {
      throw new Error('No user is currently signed in');
    }
    
    await firebaseUpdateProfile(auth.currentUser, {
      displayName,
      ...(photoURL && { photoURL })
    });
  }

  static onAuthStateChanged(callback: (user: AuthUser | null) => void): () => void {
    return onAuthStateChanged(auth, (user: User | null) => {
      callback(user ? convertFirebaseUser(user) : null);
    });
  }

  static getCurrentUser(): AuthUser | null {
    return auth.currentUser ? convertFirebaseUser(auth.currentUser) : null;
  }
} 