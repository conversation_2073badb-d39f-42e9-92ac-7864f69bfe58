import { ChecklistData, CheckStatus } from '@/types/checklist';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';

export interface StatusDistributionData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

export interface TrendData {
  date: string;
  completed: number;
  total: number;
  successRate: number;
}

export interface CategoryPerformanceData {
  category: string;
  ok: number;
  faulty: number;
  na: number;
  missing: number;
  total: number;
  successRate: number;
}

export interface TimeRangeOption {
  label: string;
  value: string;
  days: number;
}

export const TIME_RANGES: TimeRangeOption[] = [
  { label: 'Last 7 Days', value: '7d', days: 7 },
  { label: 'Last 30 Days', value: '30d', days: 30 },
  { label: 'Last 3 Months', value: '3m', days: 90 },
  { label: 'All Time', value: 'all', days: 0 }
];

export const STATUS_COLORS = {
  OK: '#22c55e',        // Green
  Faulty: '#ef4444',    // Red
  'N/A': '#f59e0b',     // Yellow/Orange
  Missing: '#6b7280'    // Gray
};

export const CHART_GRADIENTS = {
  primary: 'from-blue-400 to-teal-400',
  success: 'from-green-400 to-emerald-500',
  warning: 'from-yellow-400 to-orange-500',
  danger: 'from-red-400 to-pink-500',
  info: 'from-blue-400 to-indigo-500'
};

/**
 * Calculate status distribution for pie/donut charts
 */
export function calculateStatusDistribution(checklists: ChecklistData[]): StatusDistributionData[] {
  const statusCounts = {
    synced: 0,
    pending: 0,
    'local-only': 0,
    error: 0,
    conflict: 0
  };

  checklists.forEach(checklist => {
    const status = checklist.syncMetadata.status;
    if (status in statusCounts) {
      statusCounts[status as keyof typeof statusCounts]++;
    }
  });

  const total = checklists.length;
  
  return [
    {
      name: 'Completed',
      value: statusCounts.synced,
      percentage: total > 0 ? Math.round((statusCounts.synced / total) * 100) : 0,
      color: STATUS_COLORS.OK
    },
    {
      name: 'Pending',
      value: statusCounts.pending,
      percentage: total > 0 ? Math.round((statusCounts.pending / total) * 100) : 0,
      color: STATUS_COLORS['N/A']
    },
    {
      name: 'Local Only',
      value: statusCounts['local-only'],
      percentage: total > 0 ? Math.round((statusCounts['local-only'] / total) * 100) : 0,
      color: STATUS_COLORS.Missing
    },
    {
      name: 'Issues',
      value: statusCounts.error + statusCounts.conflict,
      percentage: total > 0 ? Math.round(((statusCounts.error + statusCounts.conflict) / total) * 100) : 0,
      color: STATUS_COLORS.Faulty
    }
  ].filter(item => item.value > 0);
}

/**
 * Generate trend data for line charts
 */
export function calculateTrendData(checklists: ChecklistData[], timeRange: string = '30d'): TrendData[] {
  const days = TIME_RANGES.find(range => range.value === timeRange)?.days || 30;
  const endDate = new Date();
  const startDate = days > 0 ? subDays(endDate, days) : new Date(Math.min(...checklists.map(c => new Date(c.createdAt).getTime())));

  const dateMap = new Map<string, { completed: number; total: number }>();

  // Initialize all dates in range
  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    const dateStr = format(d, 'MMM dd');
    dateMap.set(dateStr, { completed: 0, total: 0 });
  }

  // Count inspections by date
  checklists.forEach(checklist => {
    const checklistDate = new Date(checklist.createdAt);
    if (checklistDate >= startDate && checklistDate <= endDate) {
      const dateStr = format(checklistDate, 'MMM dd');
      const current = dateMap.get(dateStr) || { completed: 0, total: 0 };
      
      current.total++;
      if (checklist.syncMetadata.status === 'synced') {
        current.completed++;
      }
      
      dateMap.set(dateStr, current);
    }
  });

  return Array.from(dateMap.entries()).map(([date, data]) => ({
    date,
    completed: data.completed,
    total: data.total,
    successRate: data.total > 0 ? Math.round((data.completed / data.total) * 100) : 0
  }));
}

/**
 * Calculate performance by category (Mechanical, Electrical, Sequence)
 */
export function calculateCategoryPerformance(checklists: ChecklistData[]): CategoryPerformanceData[] {
  const categories = ['Mechanical', 'Electrical', 'Sequence Controls'];
  
  return categories.map(category => {
    const stats = { ok: 0, faulty: 0, na: 0, missing: 0, total: 0 };
    
    checklists.forEach(checklist => {
      let checks: any = {};
      
      switch (category) {
        case 'Mechanical':
          checks = checklist.mechanicalChecks;
          break;
        case 'Electrical':
          checks = checklist.electricalChecks;
          break;
        case 'Sequence Controls':
          checks = checklist.sequenceControlsChecks;
          break;
      }
      
      Object.values(checks).forEach(value => {
        if (typeof value === 'string') {
          const status = value as CheckStatus;
          switch (status) {
            case 'OK':
              stats.ok++;
              break;
            case 'Faulty':
              stats.faulty++;
              break;
            case 'N/A':
              stats.na++;
              break;
            case 'Missing':
              stats.missing++;
              break;
          }
          stats.total++;
        }
      });
    });
    
    return {
      category,
      ...stats,
      successRate: stats.total > 0 ? Math.round((stats.ok / stats.total) * 100) : 0
    };
  });
}

/**
 * Calculate overall equipment health score
 */
export function calculateEquipmentHealthScore(checklists: ChecklistData[]): {
  score: number;
  grade: string;
  color: string;
  trend: 'up' | 'down' | 'stable';
} {
  if (checklists.length === 0) {
    return { score: 0, grade: 'No Data', color: STATUS_COLORS.Missing, trend: 'stable' };
  }

  let totalChecks = 0;
  let okChecks = 0;
  let faultyChecks = 0;

  checklists.forEach(checklist => {
    [checklist.mechanicalChecks, checklist.electricalChecks, checklist.sequenceControlsChecks].forEach(section => {
      Object.values(section).forEach(value => {
        if (typeof value === 'string') {
          totalChecks++;
          if (value === 'OK') okChecks++;
          if (value === 'Faulty') faultyChecks++;
        }
      });
    });
  });

  const score = totalChecks > 0 ? Math.round((okChecks / totalChecks) * 100) : 0;
  
  let grade = 'Poor';
  let color = STATUS_COLORS.Faulty;
  
  if (score >= 95) {
    grade = 'Excellent';
    color = STATUS_COLORS.OK;
  } else if (score >= 85) {
    grade = 'Good';
    color = STATUS_COLORS.OK;
  } else if (score >= 70) {
    grade = 'Fair';
    color = STATUS_COLORS['N/A'];
  } else if (score >= 50) {
    grade = 'Poor';
    color = STATUS_COLORS.Faulty;
  } else {
    grade = 'Critical';
    color = STATUS_COLORS.Faulty;
  }

  // Calculate trend based on recent inspections vs older ones
  const sortedChecklists = [...checklists].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
  
  const recentCount = Math.min(5, sortedChecklists.length);
  const olderCount = Math.min(5, Math.max(0, sortedChecklists.length - 5));
  
  let trend: 'up' | 'down' | 'stable' = 'stable';
  
  if (recentCount > 0 && olderCount > 0) {
    const recentScore = calculateScoreForChecklists(sortedChecklists.slice(0, recentCount));
    const olderScore = calculateScoreForChecklists(sortedChecklists.slice(-olderCount));
    
    if (recentScore > olderScore + 5) trend = 'up';
    else if (recentScore < olderScore - 5) trend = 'down';
  }

  return { score, grade, color, trend };
}

function calculateScoreForChecklists(checklists: ChecklistData[]): number {
  let totalChecks = 0;
  let okChecks = 0;

  checklists.forEach(checklist => {
    [checklist.mechanicalChecks, checklist.electricalChecks, checklist.sequenceControlsChecks].forEach(section => {
      Object.values(section).forEach(value => {
        if (typeof value === 'string') {
          totalChecks++;
          if (value === 'OK') okChecks++;
        }
      });
    });
  });

  return totalChecks > 0 ? (okChecks / totalChecks) * 100 : 0;
}

/**
 * Get most common failure points for heatmap visualization
 */
export function getFailureAnalysis(checklists: ChecklistData[]): Array<{
  field: string;
  section: string;
  failures: number;
  totalChecks: number;
  failureRate: number;
}> {
  const fieldFailures = new Map<string, { failures: number; total: number; section: string }>();
  
  checklists.forEach(checklist => {
    // Process mechanical checks
    Object.entries(checklist.mechanicalChecks).forEach(([field, value]) => {
      if (typeof value === 'string') {
        const key = `mechanical_${field}`;
        const current = fieldFailures.get(key) || { failures: 0, total: 0, section: 'Mechanical' };
        current.total++;
        if (value === 'Faulty') current.failures++;
        fieldFailures.set(key, current);
      }
    });
    
    // Process electrical checks
    Object.entries(checklist.electricalChecks).forEach(([field, value]) => {
      if (typeof value === 'string') {
        const key = `electrical_${field}`;
        const current = fieldFailures.get(key) || { failures: 0, total: 0, section: 'Electrical' };
        current.total++;
        if (value === 'Faulty') current.failures++;
        fieldFailures.set(key, current);
      }
    });
    
    // Process sequence controls checks
    Object.entries(checklist.sequenceControlsChecks).forEach(([field, value]) => {
      if (typeof value === 'string') {
        const key = `sequence_${field}`;
        const current = fieldFailures.get(key) || { failures: 0, total: 0, section: 'Sequence Controls' };
        current.total++;
        if (value === 'Faulty') current.failures++;
        fieldFailures.set(key, current);
      }
    });
  });
  
  return Array.from(fieldFailures.entries())
    .map(([field, data]) => ({
      field: field.replace(/^(mechanical|electrical|sequence)_/, '').replace(/([A-Z])/g, ' $1').trim(),
      section: data.section,
      failures: data.failures,
      totalChecks: data.total,
      failureRate: data.total > 0 ? Math.round((data.failures / data.total) * 100) : 0
    }))
    .filter(item => item.totalChecks >= 3) // Only include fields checked at least 3 times
    .sort((a, b) => b.failureRate - a.failureRate)
    .slice(0, 15); // Top 15 most problematic fields
} 