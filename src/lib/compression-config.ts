/**
 * Image Compression Configuration
 * 
 * This file contains all compression settings that can be easily adjusted
 * for different deployment environments or user requirements.
 */

export interface CompressionEnvironmentConfig {
  mobile: {
    maxSizeKB: number;
    maxDimension: number;
    quality: number;
  };
  standard: {
    maxSizeKB: number;
    maxDimension: number;
    quality: number;
  };
  highQuality: {
    maxSizeKB: number;
    maxDimension: number;
    quality: number;
  };
  signature: {
    maxSizeKB: number;
    maxDimension: number;
    quality: number;
  };
}

// Default compression settings for Firebase hosting
export const DEFAULT_COMPRESSION_CONFIG: CompressionEnvironmentConfig = {
  mobile: {
    maxSizeKB: 300,
    maxDimension: 1280,
    quality: 0.6
  },
  standard: {
    maxSizeKB: 500,
    maxDimension: 1600,
    quality: 0.7
  },
  highQuality: {
    maxSizeKB: 1000,
    maxDimension: 1920,
    quality: 0.8
  },
  signature: {
    maxSizeKB: 200,
    maxDimension: 800,
    quality: 0.8
  }
};

// Alternative configurations for different scenarios
export const COMPRESSION_PRESETS = {
  // For very slow connections or limited storage
  ultraLight: {
    mobile: { maxSizeKB: 150, maxDimension: 1024, quality: 0.5 },
    standard: { maxSizeKB: 250, maxDimension: 1280, quality: 0.6 },
    highQuality: { maxSizeKB: 500, maxDimension: 1600, quality: 0.7 },
    signature: { maxSizeKB: 100, maxDimension: 600, quality: 0.7 }
  },

  // Balanced approach (default)
  balanced: DEFAULT_COMPRESSION_CONFIG,

  // For better quality when storage/bandwidth is less of a concern
  qualityFocused: {
    mobile: { maxSizeKB: 500, maxDimension: 1600, quality: 0.7 },
    standard: { maxSizeKB: 800, maxDimension: 1920, quality: 0.8 },
    highQuality: { maxSizeKB: 1500, maxDimension: 2560, quality: 0.85 },
    signature: { maxSizeKB: 300, maxDimension: 1000, quality: 0.85 }
  }
} as const;

// Application settings
export const APP_COMPRESSION_SETTINGS = {
  // Maximum original file size before compression (20MB)
  maxOriginalSizeMB: 20,
  
  // Supported file formats
  supportedFormats: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  
  // Whether to use web workers for compression (better performance)
  useWebWorker: true,
  
  // Progressive compression settings
  progressive: {
    enabled: true,
    maxAttempts: 5,
    qualityStep: 0.1,
    tolerancePercent: 10 // Accept files within 10% of target size
  },
  
  // UI settings
  ui: {
    showCompressionProgress: true,
    showCompressionResults: true,
    resultDisplayDurationMs: 5000,
    progressUpdateIntervalMs: 100
  }
};

// Environment-specific overrides
export function getCompressionConfigForEnvironment(): CompressionEnvironmentConfig {
  // Check if we're in development or have specific environment variables
  if (typeof window !== 'undefined') {
    // Browser environment - check for user preferences or connection quality
    
    // Check for slow connection
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection?.effectiveType === 'slow-2g' || connection?.effectiveType === '2g') {
        return COMPRESSION_PRESETS.ultraLight;
      }
    }
    
    // Check for mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if (isMobile) {
      // Use slightly more aggressive compression on mobile
      return COMPRESSION_PRESETS.balanced;
    }
  }
  
  // Check environment variables for custom settings
  if (process.env.NODE_ENV === 'development') {
    // In development, use higher quality for better debugging
    return COMPRESSION_PRESETS.qualityFocused;
  }
  
  // Production default
  return COMPRESSION_PRESETS.balanced;
}

// Helper function to get target size for a specific profile
export function getTargetSizeKB(profile: keyof CompressionEnvironmentConfig): number {
  const config = getCompressionConfigForEnvironment();
  return config[profile].maxSizeKB;
}

// Helper function to validate if a file size is acceptable after compression
export function isCompressedSizeAcceptable(
  originalSize: number, 
  compressedSize: number, 
  profile: keyof CompressionEnvironmentConfig
): boolean {
  const targetSize = getTargetSizeKB(profile) * 1024; // Convert to bytes
  const tolerance = APP_COMPRESSION_SETTINGS.progressive.tolerancePercent / 100;
  
  return compressedSize <= targetSize * (1 + tolerance);
}

// Export current active configuration
export const ACTIVE_COMPRESSION_CONFIG = getCompressionConfigForEnvironment(); 