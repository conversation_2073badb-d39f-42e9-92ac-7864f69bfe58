/**
 * Data cleanup utilities for development
 * Use these functions to clean up corrupt or incomplete data in Firestore
 */

import { collection, getDocs, doc, deleteDoc, updateDoc, query, where } from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { log } from './logger';

const CHECKLISTS_COLLECTION = 'checklists';

export interface CleanupResult {
  totalDocuments: number;
  deletedDocuments: number;
  updatedDocuments: number;
  errors: string[];
}

/**
 * Find and optionally delete documents with missing required fields
 */
export async function findCorruptDocuments(deleteCorrupt: boolean = false): Promise<CleanupResult> {
  const result: CleanupResult = {
    totalDocuments: 0,
    deletedDocuments: 0,
    updatedDocuments: 0,
    errors: []
  };

  try {
    if (!db) {
      throw new Error('Firestore database not initialized');
    }

    const checklistsRef = collection(db, CHECKLISTS_COLLECTION);
    const querySnapshot = await getDocs(checklistsRef);
    
    result.totalDocuments = querySnapshot.size;
    
    console.log(`Found ${result.totalDocuments} documents in ${CHECKLISTS_COLLECTION} collection`);

    for (const docSnapshot of querySnapshot.docs) {
      try {
        const data = docSnapshot.data();
        const docId = docSnapshot.id;
        let isCorrupt = false;
        const issues: string[] = [];

        // Check for missing required fields
        if (!data.userId) {
          issues.push('Missing userId');
          isCorrupt = true;
        }

        if (!data.createdAt && !data.updatedAt) {
          issues.push('Missing both createdAt and updatedAt');
          isCorrupt = true;
        }

        if (!data.generalInfo) {
          issues.push('Missing generalInfo');
          isCorrupt = true;
        }

        if (!data.mechanicalChecks && !data.electricalChecks && !data.sequenceControlsChecks) {
          issues.push('Missing all check sections');
          isCorrupt = true;
        }

        if (isCorrupt) {
          console.log(`Corrupt document ${docId}:`, issues);
          
          if (deleteCorrupt) {
            await deleteDoc(doc(db, CHECKLISTS_COLLECTION, docId));
            result.deletedDocuments++;
            console.log(`Deleted corrupt document: ${docId}`);
          }
        }
      } catch (error) {
        const errorMessage = `Error processing document ${docSnapshot.id}: ${error instanceof Error ? error.message : String(error)}`;
        result.errors.push(errorMessage);
        console.error(errorMessage);
      }
    }

    log.info('Document cleanup completed', 'CLEANUP', result);
    return result;

  } catch (error) {
    const errorMessage = `Cleanup failed: ${error instanceof Error ? error.message : String(error)}`;
    result.errors.push(errorMessage);
    log.error('Document cleanup failed', 'CLEANUP', error);
    throw error;
  }
}

/**
 * Find documents for a specific user
 */
export async function findUserDocuments(userId: string): Promise<{
  documents: any[];
  issues: string[];
}> {
  const documents: any[] = [];
  const issues: string[] = [];

  try {
    if (!db) {
      throw new Error('Firestore database not initialized');
    }

    const checklistsRef = collection(db, CHECKLISTS_COLLECTION);
    const q = query(checklistsRef, where('userId', '==', userId));
    const querySnapshot = await getDocs(q);

    console.log(`Found ${querySnapshot.size} documents for user ${userId}`);

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      documents.push({
        id: doc.id,
        ...data,
        docId: doc.id
      });

      // Check for potential issues
      if (!data.updatedAt) {
        issues.push(`Document ${doc.id}: Missing updatedAt`);
      }
      if (!data.generalInfo) {
        issues.push(`Document ${doc.id}: Missing generalInfo`);
      }
      if (data.userId !== userId) {
        issues.push(`Document ${doc.id}: userId mismatch (expected: ${userId}, got: ${data.userId})`);
      }
    });

    return { documents, issues };

  } catch (error) {
    console.error('Error finding user documents:', error);
    throw error;
  }
}

/**
 * Fix documents with missing timestamps
 */
export async function fixMissingTimestamps(): Promise<CleanupResult> {
  const result: CleanupResult = {
    totalDocuments: 0,
    deletedDocuments: 0,
    updatedDocuments: 0,
    errors: []
  };

  try {
    if (!db) {
      throw new Error('Firestore database not initialized');
    }

    const checklistsRef = collection(db, CHECKLISTS_COLLECTION);
    const querySnapshot = await getDocs(checklistsRef);
    
    result.totalDocuments = querySnapshot.size;

    for (const docSnapshot of querySnapshot.docs) {
      try {
        const data = docSnapshot.data();
        const docId = docSnapshot.id;
        const updates: any = {};
        let needsUpdate = false;

        // Fix missing timestamps
        const now = new Date().toISOString();
        
        if (!data.createdAt) {
          updates.createdAt = now;
          needsUpdate = true;
        }
        
        if (!data.updatedAt) {
          updates.updatedAt = now;
          needsUpdate = true;
        }

        // Fix missing basic structure
        if (!data.generalInfo) {
          updates.generalInfo = {
            clientName: '',
            building: '',
            inspectedBy: '',
            approvedBy: '',
            date: now.split('T')[0],
            ppmAttempt: 1,
            equipmentName: '',
            location: '',
            tagNo: ''
          };
          needsUpdate = true;
        }

        if (!data.mechanicalChecks) {
          updates.mechanicalChecks = {};
          needsUpdate = true;
        }

        if (!data.electricalChecks) {
          updates.electricalChecks = {};
          needsUpdate = true;
        }

        if (!data.sequenceControlsChecks) {
          updates.sequenceControlsChecks = {};
          needsUpdate = true;
        }

        if (data.remarks === undefined) {
          updates.remarks = '';
          needsUpdate = true;
        }

        if (needsUpdate) {
          await updateDoc(doc(db, CHECKLISTS_COLLECTION, docId), updates);
          result.updatedDocuments++;
          console.log(`Updated document ${docId} with missing fields:`, Object.keys(updates));
        }

      } catch (error) {
        const errorMessage = `Error updating document ${docSnapshot.id}: ${error instanceof Error ? error.message : String(error)}`;
        result.errors.push(errorMessage);
        console.error(errorMessage);
      }
    }

    log.info('Timestamp fix completed', 'CLEANUP', result);
    return result;

  } catch (error) {
    const errorMessage = `Timestamp fix failed: ${error instanceof Error ? error.message : String(error)}`;
    result.errors.push(errorMessage);
    log.error('Timestamp fix failed', 'CLEANUP', error);
    throw error;
  }
}

/**
 * Development helper: Clear all data for a specific user
 * WARNING: This will permanently delete all checklists for the user
 */
export async function clearUserData(userId: string): Promise<CleanupResult> {
  const result: CleanupResult = {
    totalDocuments: 0,
    deletedDocuments: 0,
    updatedDocuments: 0,
    errors: []
  };

  try {
    if (!db) {
      throw new Error('Firestore database not initialized');
    }

    const checklistsRef = collection(db, CHECKLISTS_COLLECTION);
    const q = query(checklistsRef, where('userId', '==', userId));
    const querySnapshot = await getDocs(q);
    
    result.totalDocuments = querySnapshot.size;

    console.log(`WARNING: About to delete ${result.totalDocuments} documents for user ${userId}`);

    for (const docSnapshot of querySnapshot.docs) {
      try {
        await deleteDoc(doc(db, CHECKLISTS_COLLECTION, docSnapshot.id));
        result.deletedDocuments++;
        console.log(`Deleted document: ${docSnapshot.id}`);
      } catch (error) {
        const errorMessage = `Error deleting document ${docSnapshot.id}: ${error instanceof Error ? error.message : String(error)}`;
        result.errors.push(errorMessage);
        console.error(errorMessage);
      }
    }

    log.info('User data cleared', 'CLEANUP', { userId, result });
    return result;

  } catch (error) {
    const errorMessage = `Clear user data failed: ${error instanceof Error ? error.message : String(error)}`;
    result.errors.push(errorMessage);
    log.error('Clear user data failed', 'CLEANUP', error);
    throw error;
  }
} 