import { ChecklistData, MechanicalCheck, ElectricalCheck, SequenceControlsCheck } from '@/types/checklist';
import { ALL_CHECKS } from '@/config/checklist-fields';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { PDFTemplate } from '@/components/pdf-template';

export function exportToExcel(checklist: ChecklistData): void {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();
    
    // Sheet 1: General Information
    const generalInfoData = [
      ['General Information', ''],
      ['Client Name', checklist.generalInfo.clientName],
      ['Building', checklist.generalInfo.building],
      ['Equipment Name', checklist.generalInfo.equipmentName],
      ['Location', checklist.generalInfo.location],
      ['Tag Number', checklist.generalInfo.tagNo],
      ['Inspection Date', checklist.generalInfo.date],
      ['PPM Attempt', checklist.generalInfo.ppmAttempt],
      ['Inspected By', checklist.generalInfo.inspectedBy],
      ['Approved By', checklist.generalInfo.approvedBy],
      [''],
      ['Remarks', ''],
      ['Notes', checklist.remarks || 'No remarks provided'],
      [''],
      ['Images', ''],
      ['Before Image', checklist.beforeImage ? 'Attached' : 'Not provided'],
      ['After Image', checklist.afterImage ? 'Attached' : 'Not provided']
    ];
    
    const generalInfoSheet = XLSX.utils.aoa_to_sheet(generalInfoData);
    
    // Set column widths for general info sheet
    generalInfoSheet['!cols'] = [
      { width: 20 },
      { width: 40 }
    ];
    
    // Apply styling to headers
    generalInfoSheet['A1'] = { v: 'General Information', t: 's' };
    generalInfoSheet['A12'] = { v: 'Remarks', t: 's' };
    generalInfoSheet['A15'] = { v: 'Images', t: 's' };
    
    XLSX.utils.book_append_sheet(workbook, generalInfoSheet, 'General Info');
    
    // Sheet 2: Mechanical Checks
    const mechanicalData = [
      ['Field', 'Value/Status', 'Unit', 'Type']
    ];
    
    ALL_CHECKS.filter(field => field.section === 'mechanical').forEach(field => {
      const value = checklist.mechanicalChecks[field.key as keyof MechanicalCheck];
      if (value !== undefined && value !== null && String(value).trim() !== '') {
        mechanicalData.push([
          field.label,
          String(value),
          field.unit || '',
          field.type === 'number' ? 'Measurement' : 'Status'
        ]);
      }
    });
    
    const mechanicalSheet = XLSX.utils.aoa_to_sheet(mechanicalData);
    mechanicalSheet['!cols'] = [
      { width: 35 },
      { width: 15 },
      { width: 10 },
      { width: 12 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, mechanicalSheet, 'Mechanical List');
    
    // Sheet 3: Electrical Checks
    const electricalData = [
      ['Field', 'Value/Status', 'Unit', 'Type']
    ];
    
    ALL_CHECKS.filter(field => field.section === 'electrical').forEach(field => {
      const value = checklist.electricalChecks[field.key as keyof ElectricalCheck];
      if (value !== undefined && value !== null && String(value).trim() !== '') {
        electricalData.push([
          field.label,
          String(value),
          field.unit || '',
          field.type === 'number' ? 'Measurement' : 'Status'
        ]);
      }
    });
    
    const electricalSheet = XLSX.utils.aoa_to_sheet(electricalData);
    electricalSheet['!cols'] = [
      { width: 35 },
      { width: 15 },
      { width: 10 },
      { width: 12 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, electricalSheet, 'Electrical List');
    
    // Sheet 4: Sequence/Controls Checks
    const sequenceData = [
      ['Field', 'Value/Status', 'Unit', 'Type']
    ];
    
    ALL_CHECKS.filter(field => field.section === 'sequence').forEach(field => {
      const value = checklist.sequenceControlsChecks[field.key as keyof SequenceControlsCheck];
      if (value !== undefined && value !== null && String(value).trim() !== '') {
        sequenceData.push([
          field.label,
          String(value),
          field.unit || '',
          field.type === 'number' ? 'Measurement' : 'Status'
        ]);
      }
    });
    
    const sequenceSheet = XLSX.utils.aoa_to_sheet(sequenceData);
    sequenceSheet['!cols'] = [
      { width: 35 },
      { width: 15 },
      { width: 10 },
      { width: 12 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, sequenceSheet, 'Sequence-Controls List');
    
    // Sheet 5: Summary
    const allItems = [
      ...Object.entries(checklist.mechanicalChecks),
      ...Object.entries(checklist.electricalChecks),
      ...Object.entries(checklist.sequenceControlsChecks)
    ];
    
    const stats = {
      OK: 0,
      Faulty: 0,
      'N/A': 0,
      Missing: 0,
      total: 0
    };
    
    allItems.forEach(([, value]) => {
      if (typeof value === 'string' && (value === 'OK' || value === 'Faulty' || value === 'N/A' || value === 'Missing')) {
        stats[value as keyof typeof stats]++;
        stats.total++;
      }
    });
    
    const okPercentage = stats.total > 0 ? Math.round((stats.OK / stats.total) * 100) : 0;
    const completionPercentage = stats.total > 0 ? Math.round(((stats.total - stats.Missing) / stats.total) * 100) : 0;
    
    const summaryData = [
      ['Inspection Summary', ''],
      [''],
      ['Status Breakdown', ''],
      ['Passed (OK)', stats.OK],
      ['Failed (Faulty)', stats.Faulty],
      ['Not Applicable (N/A)', stats['N/A']],
      ['Missing', stats.Missing],
      ['Total Tests', stats.total],
      [''],
      ['Performance Metrics', ''],
      ['Success Rate (%)', okPercentage],
      ['Completion Rate (%)', completionPercentage],
      ['Critical Issues', stats.Faulty],
      ['Assessment', okPercentage >= 90 ? 'EXCELLENT' : okPercentage >= 70 ? 'SATISFACTORY' : 'NEEDS ATTENTION'],
      [''],
      ['Report Generated', new Date().toLocaleString()]
    ];
    
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    summarySheet['!cols'] = [
      { width: 25 },
      { width: 20 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    
    // Generate and validate filename
    const fileName = generateFileName(checklist, 'xlsx');
    const validation = validateFileName(fileName);
    
    console.log('📊 Excel Export Filename:', fileName);
    console.log('📋 Structure:', validation.structure);
    
    if (!validation.isValid) {
      console.warn('⚠️ Filename validation failed:', validation.suggestions);
      // Use fallback filename if validation fails
      const fallbackFileName = generateFallbackFileName(checklist, 'xlsx');
      console.log('🔄 Using fallback filename:', fallbackFileName);
      XLSX.writeFile(workbook, fallbackFileName);
    } else {
      // Write the file with validated filename
      XLSX.writeFile(workbook, fileName);
    }
    
  } catch (error) {
    console.error('Error exporting Excel:', error);
    throw new Error('Failed to export Excel file');
  }
}

/**
 * Generate a safe fallback filename when validation fails
 */
function generateFallbackFileName(checklist: ChecklistData, extension: 'xlsx' | 'pdf'): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
  const safeId = checklist.id.substring(0, 8);
  return `PPM_Checklist_${safeId}_${timestamp}.${extension}`;
}

/**
 * Generate a structured filename for exported files
 * Format: EQUIPMENT_NAME_TAG_NUMBER_YYYY-MM-DD.extension
 * 
 * Examples:
 * - JET_FAN_JF_20_2025-01-15.pdf
 * - EXHAUST_FAN_EF_101_2024-12-03.xlsx
 * - AIR_HANDLER_AH_05A_2025-01-20.xlsx
 * 
 * @param checklist - The checklist data
 * @param extension - File extension ('xlsx' | 'pdf')
 * @returns Structured filename string
 */
function generateFileName(checklist: ChecklistData, extension: 'xlsx' | 'pdf'): string {
  try {
    const date = new Date(checklist.generalInfo.date);
    const formattedDate = format(date, 'yyyy-MM-dd');
    
    // Clean and format the tag number
    const tagNo = checklist.generalInfo.tagNo
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/_+/g, '_') // Replace multiple underscores with single
      .replace(/^_|_$/g, '') // Remove leading/trailing underscores
      .toUpperCase();
    
    // Clean and format equipment name for better readability
    const equipmentName = checklist.generalInfo.equipmentName
      .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special chars but keep spaces
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .replace(/_+/g, '_') // Replace multiple underscores with single
      .replace(/^_|_$/g, '') // Remove leading/trailing underscores
      .toUpperCase();
    
    // Generate structured filename: EQUIPMENT_TAG_DATE.extension
    return `${equipmentName}_${tagNo}_${formattedDate}.${extension}`;
  } catch {
    // Fallback to timestamp if date parsing fails
    const timestamp = new Date().toISOString().split('T')[0];
    const fallbackTag = (checklist.generalInfo.tagNo || 'UNKNOWN').replace(/[^a-zA-Z0-9]/g, '_').toUpperCase();
    const fallbackEquipment = (checklist.generalInfo.equipmentName || 'CHECKLIST').replace(/[^a-zA-Z0-9]/g, '_').toUpperCase();
    return `${fallbackEquipment}_${fallbackTag}_${timestamp}.${extension}`;
  }
}

/**
 * Validate filename structure and provide suggestions if needed
 * @param filename - The generated filename
 * @returns Object with validation result and suggestions
 */
function validateFileName(filename: string): { 
  isValid: boolean; 
  suggestions?: string[];
  structure: string;
} {
  const parts = filename.split('.');
  const nameWithoutExt = parts[0];
  const segments = nameWithoutExt.split('_');
  
  const structure = `${segments.length} segments: ${segments.join(' | ')}`;
  
  if (segments.length < 3) {
    return {
      isValid: false,
      suggestions: [
        'Filename should have at least 3 segments: EQUIPMENT_TAG_DATE',
        'Example: JET_FAN_JF_20_2025-01-15.pdf'
      ],
      structure
    };
  }
  
  // Check if last segment looks like a date
  const lastSegment = segments[segments.length - 1];
  const datePattern = /^\d{4}-\d{2}-\d{2}$/;
  
  if (!datePattern.test(lastSegment)) {
    return {
      isValid: false,
      suggestions: [
        'Last segment should be a date in YYYY-MM-DD format',
        `Current: ${lastSegment}, Expected format: 2025-01-15`
      ],
      structure
    };
  }
  
  return {
    isValid: true,
    structure
  };
}

export async function exportToPDF(checklist: ChecklistData) {
  // Create a temporary container
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.left = '-9999px';
  container.style.top = '-9999px';
  container.style.width = '210mm';
  container.style.backgroundColor = '#ffffff';
  container.style.fontFamily = 'Arial, sans-serif';
  document.body.appendChild(container);

  try {
    // Create root for React rendering
    const root = createRoot(container);
    
    // Render the PDF template
    await new Promise<void>((resolve) => {
      root.render(React.createElement(PDFTemplate, { checklist }));
      // Wait for React to render completely
      setTimeout(resolve, 1500); // Increased timeout for complex layouts
    });

    // Convert the template to canvas with improved settings
    const canvas = await html2canvas(container, {
      scale: 1.5, // Reduced scale to avoid memory issues
      useCORS: true,
      logging: false,
      backgroundColor: '#ffffff',
      allowTaint: false, // More strict to avoid tainted canvas
      removeContainer: false,
      width: container.scrollWidth,
      height: container.scrollHeight,
      foreignObjectRendering: false, // Disable to avoid rendering issues
      imageTimeout: 5000, // Add timeout for image processing
      onclone: (clonedDoc) => {
        // Ensure all styles are properly applied in the cloned document
        const style = clonedDoc.createElement('style');
        style.textContent = `
          * { 
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
        `;
        clonedDoc.head.appendChild(style);
      }
    });

    // Validate canvas before proceeding
    if (!canvas || canvas.width === 0 || canvas.height === 0) {
      throw new Error('Canvas generation failed - invalid dimensions');
    }

    // Create PDF with proper dimensions
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const pageWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;

    // Calculate the width and height for the PDF
    const ratio = pageWidth / canvasWidth;
    const scaledHeight = canvasHeight * ratio;

    try {
      if (scaledHeight <= pageHeight) {
        // Content fits on one page
        const imageData = canvas.toDataURL('image/png', 0.95); // Slightly lower quality to avoid corruption
        
        // Validate the image data
        if (!imageData || imageData === 'data:,' || imageData.length < 100) {
          throw new Error('Generated image data is invalid or corrupted');
        }

        pdf.addImage(
          imageData,
          'PNG',
          0,
          0,
          pageWidth,
          scaledHeight
        );
      } else {
        // Content needs multiple pages
        const pageCanvasHeight = pageHeight / ratio;
        let yPosition = 0;
        let pageNumber = 1;

        while (yPosition < canvasHeight) {
          // Create a new canvas for this page
          const pageCanvas = document.createElement('canvas');
          const pageCtx = pageCanvas.getContext('2d');
          
          if (!pageCtx) {
            throw new Error('Failed to get 2D context for page canvas');
          }

          pageCanvas.width = canvasWidth;
          pageCanvas.height = Math.min(pageCanvasHeight, canvasHeight - yPosition);

          // Fill with white background
          pageCtx.fillStyle = '#ffffff';
          pageCtx.fillRect(0, 0, pageCanvas.width, pageCanvas.height);
          
          // Draw the portion of the original canvas
          pageCtx.drawImage(
            canvas,
            0, yPosition, canvasWidth, pageCanvas.height,
            0, 0, canvasWidth, pageCanvas.height
          );

          // Generate image data for this page
          const pageImageData = pageCanvas.toDataURL('image/png', 0.95);
          
          // Validate the page image data
          if (!pageImageData || pageImageData === 'data:,' || pageImageData.length < 100) {
            throw new Error(`Generated image data for page ${pageNumber} is invalid or corrupted`);
          }

          // Add page to PDF (add new page if not the first)
          if (pageNumber > 1) {
            pdf.addPage();
          }

          pdf.addImage(
            pageImageData,
            'PNG',
            0,
            0,
            pageWidth,
            (pageCanvas.height * ratio)
          );

          yPosition += pageCanvasHeight;
          pageNumber++;
        }
      }
    } catch (imageError) {
      console.error('Error processing images for PDF:', imageError);
      throw new Error(`PDF image processing failed: ${imageError instanceof Error ? imageError.message : 'Unknown error'}`);
    }

    // Generate filename with validation
    const filename = generateFileName(checklist, 'pdf');
    
    // Log filename structure for debugging
    const validation = validateFileName(filename);
    console.log('📄 PDF Export Filename:', filename);
    console.log('📋 Structure:', validation.structure);
    if (!validation.isValid) {
      console.warn('⚠️ Filename validation issues:', validation.suggestions);
    }

    // Save the PDF
    pdf.save(filename);

    // Clean up React root
    root.unmount();
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error(`PDF export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    // Clean up DOM
    if (document.body.contains(container)) {
      document.body.removeChild(container);
    }
  }
}

// Export utility functions for testing and debugging
export { generateFileName as _generateFileName, validateFileName as _validateFileName }; 