import { ChecklistData, SyncMetadata } from '@/types/checklist';
import { log } from './logger';
import { 
  collection, 
  doc, 
  setDoc, 
  getDoc,
  getDocs, 
  deleteDoc,
  updateDoc,
  query, 
  where,
  orderBy,
  onSnapshot,
  writeBatch,
  serverTimestamp,
  Timestamp,
  enableNetwork,
  disableNetwork,
  clearIndexedDbPersistence,
  FieldValue
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { MigrationService } from './migration-service';

export interface StorageStatus {
  available: boolean;
  used: number;
  quota: number;
  type: 'firestore' | 'localStorage';
  isOnline: boolean;
  lastSync?: string;
}

// Define the SyncStatus interface that storage adapter expects
export interface SyncStatusInfo {
  inProgress: boolean;
  lastSync: string | null;
  hasChanges: boolean;
  isOnline: boolean;
  queueSize: number;
}

export interface StorageSettings {
  autoSync: boolean;
  cacheSize: number;
  compressionEnabled: boolean;
  showStorageWarnings?: boolean;
  daysToKeepCompleted?: number;
  daysToKeepSynced?: number;
  userId: string;
}

/**
 * Firestore-based storage service with offline persistence
 * Replaces the custom localStorage implementation
 */
export class FirestoreStorageService {
  private static readonly CHECKLISTS_COLLECTION = 'checklists';
  private static readonly SETTINGS_COLLECTION = 'settings';
  private static readonly DEFAULT_SETTINGS: Partial<StorageSettings> = {
    autoSync: true,
    cacheSize: 100 * 1024 * 1024, // 100MB
    compressionEnabled: true,
    showStorageWarnings: true,
    daysToKeepCompleted: 30,
    daysToKeepSynced: 7
  };

  private static listeners: Map<string, () => void> = new Map();
  private static isOnline = navigator.onLine;
  private static lastSyncTimestamp: string | null = null;

  /**
   * Initialize the storage service
   */
  static async initialize(userId: string): Promise<void> {
    log.info('Initializing Firestore storage service', 'STORAGE', { userId });

    // Check if migration is needed
    if (MigrationService.isMigrationNeeded()) {
      log.info('Migration needed, executing migration', 'STORAGE', {});
      const result = await MigrationService.executeMigration(userId);
      
      if (!result.success) {
        throw new Error(`Migration failed: ${result.errors.join(', ')}`);
      }
      
      log.info('Migration completed successfully', 'STORAGE', {
        migratedCount: result.migratedCount,
        duration: result.duration
      });
    }

    // Initialize settings if they don't exist
    await this.initializeSettings(userId);

    // Set up network status monitoring
    this.setupNetworkMonitoring();

    log.info('Firestore storage service initialized', 'STORAGE', {
      userId,
      isOnline: this.isOnline
    });
  }

  /**
   * Save a checklist to Firestore
   */
  static async saveChecklist(checklist: ChecklistData, userId: string): Promise<void> {
    try {
      const docData = {
        ...checklist,
        userId,
        updatedAt: new Date().toISOString(),
        createdAt: checklist.createdAt || new Date().toISOString()
      };

      const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklist.id);
      await setDoc(docRef, docData);

      this.lastSyncTimestamp = new Date().toISOString();
      
      log.debug('Checklist saved to Firestore', 'STORAGE', {
        checklistId: checklist.id,
        userId,
        isOnline: this.isOnline
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to save checklist to Firestore', 'STORAGE', {
        checklistId: checklist.id,
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to save checklist: ${errorMessage}`);
    }
  }

  /**
   * Get a specific checklist by ID
   */
  static async getChecklist(checklistId: string, userId: string): Promise<ChecklistData | null> {
    try {
      const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklistId);
      const docSnapshot = await getDoc(docRef);

      if (!docSnapshot.exists()) {
        return null;
      }

      const data = docSnapshot.data();
      
      // Verify user ownership
      if (data.userId !== userId) {
        log.warn('Attempted to access checklist from different user', 'STORAGE', {
          checklistId,
          requestedBy: userId,
          actualOwner: data.userId
        });
        return null;
      }

      return {
        ...data,
        id: docSnapshot.id,
        createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : data.updatedAt,
        completedAt: data.completedAt instanceof Timestamp ? data.completedAt.toDate().toISOString() : data.completedAt
      } as ChecklistData;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to get checklist from Firestore', 'STORAGE', {
        checklistId,
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to get checklist: ${errorMessage}`);
    }
  }

  /**
   * Get all checklists for a user
   */
  static async getAllChecklists(userId: string): Promise<ChecklistData[]> {
    try {
      const q = query(
        collection(db, this.CHECKLISTS_COLLECTION),
        where('userId', '==', userId),
        orderBy('updatedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const checklists: ChecklistData[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        checklists.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : data.createdAt,
          updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : data.updatedAt,
          completedAt: data.completedAt instanceof Timestamp ? data.completedAt.toDate().toISOString() : data.completedAt
        } as ChecklistData);
      });

      this.lastSyncTimestamp = new Date().toISOString();

      log.debug('Retrieved all checklists from Firestore', 'STORAGE', {
        userId,
        count: checklists.length,
        isOnline: this.isOnline
      });

      return checklists;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to get all checklists from Firestore', 'STORAGE', {
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to get checklists: ${errorMessage}`);
    }
  }

  /**
   * Delete a checklist
   */
  static async deleteChecklist(checklistId: string, userId: string): Promise<void> {
    try {
      // Verify ownership before deletion
      const checklist = await this.getChecklist(checklistId, userId);
      if (!checklist) {
        throw new Error('Checklist not found or access denied');
      }

      const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklistId);
      await deleteDoc(docRef);

      log.info('Checklist deleted from Firestore', 'STORAGE', {
        checklistId,
        userId
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to delete checklist from Firestore', 'STORAGE', {
        checklistId,
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to delete checklist: ${errorMessage}`);
    }
  }

  /**
   * Update checklist completion status
   */
  static async updateChecklistStatus(
    checklistId: string, 
    userId: string, 
    isCompleted: boolean,
    completedAt?: string
  ): Promise<void> {
    try {
      const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklistId);
      const updateData: any = {
        isCompleted,
        updatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString()
      };

      if (isCompleted && completedAt) {
        updateData.completedAt = completedAt;
      } else if (!isCompleted) {
        updateData.completedAt = null;
      }

      await updateDoc(docRef, updateData);

      log.debug('Checklist status updated in Firestore', 'STORAGE', {
        checklistId,
        userId,
        isCompleted
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to update checklist status in Firestore', 'STORAGE', {
        checklistId,
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to update checklist status: ${errorMessage}`);
    }
  }

  /**
   * Bulk save multiple checklists
   */
  static async bulkSaveChecklists(checklists: ChecklistData[], userId: string): Promise<void> {
    try {
      const batch = writeBatch(db);
      const timestamp = new Date().toISOString();

      checklists.forEach((checklist) => {
        const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklist.id);
        const docData = {
          ...checklist,
          userId,
          updatedAt: timestamp,
          createdAt: checklist.createdAt || timestamp
        };

        batch.set(docRef, docData);
      });

      await batch.commit();

      this.lastSyncTimestamp = timestamp;

      log.info('Bulk save completed in Firestore', 'STORAGE', {
        userId,
        count: checklists.length
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to bulk save checklists to Firestore', 'STORAGE', {
        userId,
        count: checklists.length,
        error: errorMessage
      });
      throw new Error(`Failed to bulk save checklists: ${errorMessage}`);
    }
  }

  /**
   * Subscribe to real-time updates for user's checklists
   */
  static subscribeToChecklists(
    userId: string, 
    callback: (checklists: ChecklistData[]) => void
  ): () => void {
    const q = query(
      collection(db, this.CHECKLISTS_COLLECTION),
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const checklists: ChecklistData[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        checklists.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : data.createdAt,
          updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : data.updatedAt,
          completedAt: data.completedAt instanceof Timestamp ? data.completedAt.toDate().toISOString() : data.completedAt
        } as ChecklistData);
      });

      this.lastSyncTimestamp = new Date().toISOString();

      callback(checklists);

      log.debug('Real-time update received', 'STORAGE', {
        userId,
        count: checklists.length
      });
    }, (error) => {
      log.error('Real-time subscription error', 'STORAGE', {
        userId,
        error: error.message
      });
    });

    // Store unsubscribe function
    this.listeners.set(userId, unsubscribe);

    return unsubscribe;
  }

  /**
   * Unsubscribe from real-time updates
   */
  static unsubscribeFromChecklists(userId: string): void {
    const unsubscribe = this.listeners.get(userId);
    if (unsubscribe) {
      unsubscribe();
      this.listeners.delete(userId);
      log.debug('Unsubscribed from real-time updates', 'STORAGE', { userId });
    }
  }

  /**
   * Get storage status and statistics
   */
  static async getStorageStatus(userId: string): Promise<StorageStatus> {
    try {
      const checklists = await this.getAllChecklists(userId);
      const dataSize = JSON.stringify(checklists).length;

      return {
        available: true,
        used: dataSize,
        quota: 100 * 1024 * 1024, // 100MB Firestore cache
        type: 'firestore',
        isOnline: this.isOnline,
        lastSync: this.lastSyncTimestamp || undefined
      };

    } catch (error) {
      log.error('Failed to get storage status', 'STORAGE', { userId, error });
      return {
        available: false,
        used: 0,
        quota: 0,
        type: 'firestore',
        isOnline: this.isOnline
      };
    }
  }

  /**
   * Get storage settings
   */
  static async getSettings(userId: string): Promise<StorageSettings> {
    try {
      const docRef = doc(db, this.SETTINGS_COLLECTION, userId);
      const docSnapshot = await getDoc(docRef);

      if (docSnapshot.exists()) {
        return {
          ...this.DEFAULT_SETTINGS,
          ...docSnapshot.data(),
          userId
        } as StorageSettings;
      }

      // Return default settings if none exist
      return {
        ...this.DEFAULT_SETTINGS,
        userId
      } as StorageSettings;

    } catch (error) {
      log.error('Failed to get storage settings', 'STORAGE', { userId, error });
      return {
        ...this.DEFAULT_SETTINGS,
        userId
      } as StorageSettings;
    }
  }

  /**
   * Update storage settings
   */
  static async updateSettings(userId: string, settings: Partial<StorageSettings>): Promise<void> {
    try {
      const docRef = doc(db, this.SETTINGS_COLLECTION, userId);
      await setDoc(docRef, {
        ...settings,
        userId,
        updatedAt: new Date().toISOString()
      }, { merge: true });

      log.info('Storage settings updated', 'STORAGE', { userId, settings });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to update storage settings', 'STORAGE', {
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to update settings: ${errorMessage}`);
    }
  }

  /**
   * Clear all user data (for logout/reset)
   */
  static async clearUserData(userId: string): Promise<void> {
    try {
      // Unsubscribe from real-time updates
      this.unsubscribeFromChecklists(userId);

      // Note: We don't delete data from Firestore on logout
      // This preserves data across sessions and devices
      
      log.info('User data cleared from memory', 'STORAGE', { userId });

    } catch (error) {
      log.error('Failed to clear user data', 'STORAGE', { userId, error });
      throw error;
    }
  }

  /**
   * Check network connectivity and sync status
   */
  static getNetworkStatus(): { isOnline: boolean; lastSync?: string } {
    return {
      isOnline: this.isOnline,
      lastSync: this.lastSyncTimestamp || undefined
    };
  }

  /**
   * Manually enable/disable network for testing
   */
  static async setNetworkEnabled(enabled: boolean): Promise<void> {
    try {
      if (enabled) {
        await enableNetwork(db);
        log.info('Firestore network enabled', 'STORAGE', {});
      } else {
        await disableNetwork(db);
        log.info('Firestore network disabled', 'STORAGE', {});
      }
    } catch (error) {
      log.error('Failed to change network status', 'STORAGE', { enabled, error });
      throw error;
    }
  }

  /**
   * Initialize default settings for a user
   */
  private static async initializeSettings(userId: string): Promise<void> {
    try {
      const docRef = doc(db, this.SETTINGS_COLLECTION, userId);
      const docSnapshot = await getDoc(docRef);

      if (!docSnapshot.exists()) {
        await setDoc(docRef, {
          ...this.DEFAULT_SETTINGS,
          userId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });

        log.info('Default settings initialized', 'STORAGE', { userId });
      }

    } catch (error) {
      log.error('Failed to initialize settings', 'STORAGE', { userId, error });
      // Don't throw error - app can continue without settings
    }
  }

  /**
   * Set up network status monitoring
   */
  private static setupNetworkMonitoring(): void {
    const updateNetworkStatus = () => {
      const wasOnline = this.isOnline;
      this.isOnline = navigator.onLine;

      if (wasOnline !== this.isOnline) {
        log.info('Network status changed', 'STORAGE', {
          isOnline: this.isOnline,
          wasOnline
        });
      }
    };

    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    // Initial status
    updateNetworkStatus();
  }

  /**
   * Get sync status for the UI
   */
  static getSyncStatus(): SyncStatusInfo {
    return {
      inProgress: false, // Firestore handles sync automatically
      lastSync: this.lastSyncTimestamp || null,
      hasChanges: false, // No manual queue with Firestore
      isOnline: this.isOnline,
      queueSize: 0 // No manual queue
    };
  }

  /**
   * Refresh the local cache
   */
  static async refreshCache(): Promise<void> {
    try {
      // Force refresh by clearing and re-enabling the cache
      await this.clearLocalCache();
      log.info('Firestore cache refreshed', 'STORAGE', {});
    } catch (error) {
      log.error('Failed to refresh cache', 'STORAGE', { error });
      throw error;
    }
  }

  /**
   * Clear the local Firestore cache
   */
  static async clearLocalCache(): Promise<void> {
    try {
      await clearIndexedDbPersistence(db);
      log.info('Firestore local cache cleared', 'STORAGE', {});
    } catch (error) {
      log.error('Failed to clear local cache', 'STORAGE', { error });
      // If clearIndexedDbPersistence fails, we can try disabling and re-enabling network
      try {
        await disableNetwork(db);
        await enableNetwork(db);
        log.info('Network reset as fallback for cache clear', 'STORAGE', {});
      } catch (networkError) {
        log.error('Failed to reset network as cache clear fallback', 'STORAGE', { error: networkError });
        throw error; // Throw original error
      }
    }
  }
} 