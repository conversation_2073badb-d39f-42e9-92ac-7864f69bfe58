import imageCompression from 'browser-image-compression';

// Compression configuration types
export interface CompressionConfig {
  maxSizeMB: number;
  maxWidthOrHeight: number;
  useWebWorker: boolean;
  quality: number;
  fileType?: string;
}

export interface CompressionResult {
  compressedFile: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  base64: string;
}

export interface CompressionOptions {
  profile: 'mobile' | 'standard' | 'high-quality' | 'signature' | 'custom';
  customConfig?: Partial<CompressionConfig>;
  targetSizeKB?: number;
  preserveTransparency?: boolean;
}

// Predefined compression profiles
const COMPRESSION_PROFILES: Record<string, CompressionConfig> = {
  mobile: {
    maxSizeMB: 0.3, // 300KB
    maxWidthOrHeight: 1280,
    useWebWorker: true,
    quality: 0.6,
    fileType: 'image/jpeg'
  },
  standard: {
    maxSizeMB: 0.5, // 500KB
    maxWidthOrHeight: 1600,
    useWebWorker: true,
    quality: 0.7,
    fileType: 'image/jpeg'
  },
  'high-quality': {
    maxSizeMB: 1.0, // 1MB
    maxWidthOrHeight: 1920,
    useWebWorker: true,
    quality: 0.8,
    fileType: 'image/jpeg'
  },
  signature: {
    maxSizeMB: 0.2, // 200KB
    maxWidthOrHeight: 800,
    useWebWorker: true,
    quality: 0.8,
    fileType: 'image/png' // Preserve transparency for signatures
  }
};

/**
 * Get optimal compression configuration based on file and options
 */
function getCompressionConfig(file: File, options: CompressionOptions): CompressionConfig {
  const baseConfig = COMPRESSION_PROFILES[options.profile];
  
  // Apply custom overrides
  const config: CompressionConfig = {
    ...baseConfig,
    ...options.customConfig
  };

  // Smart format selection
  if (!options.customConfig?.fileType) {
    if (options.preserveTransparency || file.type === 'image/png') {
      config.fileType = 'image/png';
    } else {
      config.fileType = 'image/jpeg';
    }
  }

  // Adjust for signature images
  if (options.profile === 'signature') {
    config.fileType = 'image/png'; // Always preserve transparency for signatures
  }

  return config;
}

/**
 * Progressive compression - automatically adjusts quality to meet target size
 */
async function progressiveCompress(
  file: File, 
  config: CompressionConfig, 
  targetSizeKB?: number
): Promise<File> {
  if (!targetSizeKB) {
    return await imageCompression(file, config);
  }

  const targetSizeMB = targetSizeKB / 1024;
  let currentQuality = config.quality;
  let attempts = 0;
  const maxAttempts = 5;
  const qualityStep = 0.1;

  while (attempts < maxAttempts) {
    const attemptConfig = {
      ...config,
      quality: currentQuality,
      maxSizeMB: targetSizeMB
    };

    try {
      const compressed = await imageCompression(file, attemptConfig);
      
      // If we're within 10% of target size, we're good
      const sizeDifferencePercent = Math.abs(compressed.size - (targetSizeKB * 1024)) / (targetSizeKB * 1024);
      if (sizeDifferencePercent <= 0.1 || compressed.size <= targetSizeKB * 1024) {
        return compressed;
      }

      // If still too large, reduce quality
      if (compressed.size > targetSizeKB * 1024 && currentQuality > 0.3) {
        currentQuality -= qualityStep;
        attempts++;
      } else {
        return compressed;
      }
    } catch (error) {
      console.warn(`Compression attempt ${attempts + 1} failed:`, error);
      attempts++;
      currentQuality = Math.max(0.3, currentQuality - qualityStep);
    }
  }

  // Fallback: use the most aggressive compression we can
  const fallbackConfig = {
    ...config,
    quality: 0.3,
    maxSizeMB: targetSizeMB * 1.5 // Allow slightly larger if needed
  };
  
  return await imageCompression(file, fallbackConfig);
}

/**
 * Convert compressed file to base64
 */
async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
}

/**
 * Main compression function
 */
export async function compressImage(
  file: File, 
  options: CompressionOptions = { profile: 'standard' }
): Promise<CompressionResult> {
  // Validate input
  if (!file.type.startsWith('image/')) {
    throw new Error('File must be an image');
  }

  const originalSize = file.size;
  
  try {
    // Get compression configuration
    const config = getCompressionConfig(file, options);
    
    // Perform compression
    const compressedFile = await progressiveCompress(file, config, options.targetSizeKB);
    
    // Convert to base64
    const base64 = await fileToBase64(compressedFile);
    
    // Calculate compression metrics
    const compressedSize = compressedFile.size;
    const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;
    
    return {
      compressedFile,
      originalSize,
      compressedSize,
      compressionRatio,
      base64
    };
  } catch (error) {
    console.error('Image compression failed:', error);
    throw new Error(`Image compression failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Quick compression for common use cases
 */
export const quickCompress = {
  /**
   * Compress for mobile/slow connections
   */
  mobile: (file: File) => compressImage(file, { 
    profile: 'mobile',
    targetSizeKB: 300 
  }),

  /**
   * Standard compression for most use cases
   */
  standard: (file: File) => compressImage(file, { 
    profile: 'standard',
    targetSizeKB: 500 
  }),

  /**
   * High quality for important images
   */
  highQuality: (file: File) => compressImage(file, { 
    profile: 'high-quality',
    targetSizeKB: 1000 
  }),

  /**
   * Optimized for signature images
   */
  signature: (file: File) => compressImage(file, { 
    profile: 'signature',
    targetSizeKB: 200,
    preserveTransparency: true 
  })
};

/**
 * Get human-readable file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { isValid: boolean; error?: string } {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return { isValid: false, error: 'File must be an image' };
  }

  // Check file size (original limit before compression)
  const maxOriginalSize = 20 * 1024 * 1024; // 20MB original limit
  if (file.size > maxOriginalSize) {
    return { 
      isValid: false, 
      error: `Original image size must be less than ${formatFileSize(maxOriginalSize)}` 
    };
  }

  // Check supported formats
  const supportedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  if (!supportedTypes.includes(file.type)) {
    return { 
      isValid: false, 
      error: 'Supported formats: JPEG, PNG, WebP, GIF' 
    };
  }

  return { isValid: true };
}

/**
 * Auto-select compression profile based on connection and device
 */
export function getRecommendedProfile(): 'mobile' | 'standard' | 'high-quality' {
  // Check connection speed if available
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    if (connection?.effectiveType === 'slow-2g' || connection?.effectiveType === '2g') {
      return 'mobile';
    }
  }

  // Check if mobile device
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  if (isMobile) {
    return 'mobile';
  }

  return 'standard';
} 