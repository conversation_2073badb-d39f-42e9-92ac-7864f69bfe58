/**
 * Comprehensive logging service for Auburn PPM Suite
 * Provides structured logging with different levels and optional remote logging
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: string;
  data?: any;
  userId?: string;
  sessionId?: string;
  error?: Error;
}

class Logger {
  private static instance: Logger;
  private logs: LogEntry[] = [];
  private maxLogs = 1000; // Keep last 1000 logs in memory
  private sessionId: string;
  private currentLogLevel: LogLevel = LogLevel.INFO;

  private constructor() {
    this.sessionId = this.generateSessionId();
    
    // Set log level based on environment
    if (typeof window !== 'undefined') {
      this.currentLogLevel = process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO;
    }
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.currentLogLevel;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: string,
    data?: any,
    error?: Error
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      data,
      sessionId: this.sessionId,
      error
    };
  }

  private addLog(entry: LogEntry): void {
    this.logs.push(entry);
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output with appropriate method
    const logMethod = this.getConsoleMethod(entry.level);
    const prefix = `[${entry.level === LogLevel.DEBUG ? 'DEBUG' : 
                      entry.level === LogLevel.INFO ? 'INFO' : 
                      entry.level === LogLevel.WARN ? 'WARN' : 
                      entry.level === LogLevel.ERROR ? 'ERROR' : 'CRITICAL'}]`;
    
    const contextStr = entry.context ? ` [${entry.context}]` : '';
    
    if (entry.data || entry.error) {
      logMethod(`${prefix}${contextStr} ${entry.message}`, entry.data || entry.error);
    } else {
      logMethod(`${prefix}${contextStr} ${entry.message}`);
    }
  }

  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LogLevel.DEBUG:
        return console.debug;
      case LogLevel.INFO:
        return console.info;
      case LogLevel.WARN:
        return console.warn;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        return console.error;
      default:
        return console.log;
    }
  }

  debug(message: string, context?: string, data?: any): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.addLog(this.createLogEntry(LogLevel.DEBUG, message, context, data));
    }
  }

  info(message: string, context?: string, data?: any): void {
    if (this.shouldLog(LogLevel.INFO)) {
      this.addLog(this.createLogEntry(LogLevel.INFO, message, context, data));
    }
  }

  warn(message: string, context?: string, data?: any): void {
    if (this.shouldLog(LogLevel.WARN)) {
      this.addLog(this.createLogEntry(LogLevel.WARN, message, context, data));
    }
  }

  error(message: string, context?: string, error?: Error | any): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      this.addLog(this.createLogEntry(LogLevel.ERROR, message, context, undefined, error));
    }
  }

  critical(message: string, context?: string, error?: Error | any): void {
    if (this.shouldLog(LogLevel.CRITICAL)) {
      this.addLog(this.createLogEntry(LogLevel.CRITICAL, message, context, undefined, error));
    }
  }

  // Specialized logging methods for common operations
  syncOperation(operation: string, result: 'success' | 'failure', details?: any): void {
    const level = result === 'success' ? LogLevel.INFO : LogLevel.ERROR;
    this.addLog(this.createLogEntry(
      level,
      `Sync operation: ${operation} - ${result}`,
      'SYNC',
      details
    ));
  }

  userAction(action: string, userId?: string, details?: any): void {
    this.addLog(this.createLogEntry(
      LogLevel.INFO,
      `User action: ${action}`,
      'USER_ACTION',
      { ...details, userId }
    ));
  }

  storageOperation(operation: string, result: 'success' | 'failure', details?: any): void {
    const level = result === 'success' ? LogLevel.DEBUG : LogLevel.ERROR;
    this.addLog(this.createLogEntry(
      level,
      `Storage operation: ${operation} - ${result}`,
      'STORAGE',
      details
    ));
  }

  // Get logs for debugging or export
  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level);
    }
    return [...this.logs];
  }

  // Export logs as JSON for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  // Clear logs (useful for testing or memory management)
  clearLogs(): void {
    this.logs = [];
    this.info('Logs cleared', 'LOGGER');
  }

  // Get session information
  getSessionInfo(): { sessionId: string; logCount: number; currentLevel: LogLevel } {
    return {
      sessionId: this.sessionId,
      logCount: this.logs.length,
      currentLevel: this.currentLogLevel
    };
  }
}

// Export singleton instance
export const logger = Logger.getInstance();

// Export convenience functions
export const log = {
  debug: (message: string, context?: string, data?: any) => logger.debug(message, context, data),
  info: (message: string, context?: string, data?: any) => logger.info(message, context, data),
  warn: (message: string, context?: string, data?: any) => logger.warn(message, context, data),
  error: (message: string, context?: string, error?: Error | any) => logger.error(message, context, error),
  critical: (message: string, context?: string, error?: Error | any) => logger.critical(message, context, error),
  sync: (operation: string, result: 'success' | 'failure', details?: any) => logger.syncOperation(operation, result, details),
  user: (action: string, userId?: string, details?: any) => logger.userAction(action, userId, details),
  storage: (operation: string, result: 'success' | 'failure', details?: any) => logger.storageOperation(operation, result, details)
}; 