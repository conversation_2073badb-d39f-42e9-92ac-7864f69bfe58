import { ChecklistData, SyncStatus } from '@/types/checklist';
import { log } from './logger';
import { 
  collection, 
  doc, 
  setDoc, 
  getDocs, 
  writeBatch, 
  query, 
  where,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';

interface MigrationStatus {
  phase: 'backup' | 'migrate' | 'validate' | 'complete' | 'error';
  startedAt: string;
  lastUpdated: string;
  progress: {
    total: number;
    completed: number;
    failed: number;
  };
  errors: string[];
  backupCreated: boolean;
}

interface MigrationBackup {
  timestamp: string;
  version: string;
  checklists: ChecklistData[];
  syncQueue: any[];
  settings: any;
  userAgent: string;
}

interface MigrationResult {
  success: boolean;
  migratedCount: number;
  skippedCount: number;
  errors: string[];
  duration: number;
  canRollback: boolean;
}

export class MigrationService {
  private static readonly MIGRATION_STATUS_KEY = 'firestore-migration-status';
  private static readonly BACKUP_DATA_KEY = 'firestore-migration-backup';
  private static readonly ORIGINAL_STORAGE_KEY = 'ppm-checklists';
  private static readonly SYNC_QUEUE_KEY = 'sync_queue';
  private static readonly SETTINGS_KEY = 'ppm-storage-settings';
  private static readonly CHECKLISTS_COLLECTION = 'checklists';
  
  private static migrationInProgress = false;

  /**
   * Execute complete migration with backup and validation
   */
  static async executeMigration(userId: string): Promise<MigrationResult> {
    if (this.migrationInProgress) {
      throw new Error('Migration already in progress');
    }

    this.migrationInProgress = true;
    const startTime = Date.now();
    
    const result: MigrationResult = {
      success: false,
      migratedCount: 0,
      skippedCount: 0,
      errors: [],
      duration: 0,
      canRollback: false
    };

    try {
      log.info('Starting complete migration to Firestore persistence', 'MIGRATION', { userId });

      // Phase 1: Create backup
      await this.createBackup();
      result.canRollback = true;
      
      // Phase 2: Process sync queue
      await this.processSyncQueue(userId);
      
      // Phase 3: Migrate all checklists
      const migrationResults = await this.migrateAllChecklists(userId);
      result.migratedCount = migrationResults.migrated;
      result.skippedCount = migrationResults.skipped;
      result.errors.push(...migrationResults.errors);

      // Phase 4: Validate migration
      const validationResult = await this.validateMigration(userId);
      if (!validationResult.isValid) {
        result.errors.push(...validationResult.errors);
        throw new Error('Migration validation failed');
      }

      // Phase 5: Cleanup and mark complete
      await this.completeMigration();
      
      result.success = true;
      result.duration = Date.now() - startTime;

      log.info('Migration completed successfully', 'MIGRATION', {
        userId,
        duration: result.duration,
        migratedCount: result.migratedCount
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(errorMessage);
      
      log.error('Migration failed', 'MIGRATION', {
        userId,
        error: errorMessage,
        phase: this.getCurrentPhase()
      });

      // Update status as error
      await this.updateMigrationStatus({
        phase: 'error',
        errors: result.errors
      });

    } finally {
      this.migrationInProgress = false;
      result.duration = Date.now() - startTime;
    }

    return result;
  }

  /**
   * Create comprehensive backup of all current data
   */
  private static async createBackup(): Promise<void> {
    await this.updateMigrationStatus({
      phase: 'backup',
      progress: { total: 1, completed: 0, failed: 0 }
    });

    try {
      // Get all current data
      const checklists = this.getLocalChecklists();
      const syncQueue = this.getSyncQueue();
      const settings = this.getStorageSettings();

      const backup: MigrationBackup = {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        checklists,
        syncQueue,
        settings,
        userAgent: navigator.userAgent
      };

      // Store backup
      localStorage.setItem(this.BACKUP_DATA_KEY, JSON.stringify(backup));

      await this.updateMigrationStatus({
        phase: 'backup',
        progress: { total: 1, completed: 1, failed: 0 },
        backupCreated: true
      });

      log.info('Backup created successfully', 'MIGRATION', {
        checklistCount: checklists.length,
        syncQueueCount: syncQueue.length
      });

    } catch (error) {
      throw new Error(`Backup creation failed: ${error}`);
    }
  }

  /**
   * Process existing sync queue before migration
   */
  private static async processSyncQueue(userId: string): Promise<void> {
    const syncQueue = this.getSyncQueue();
    if (syncQueue.length === 0) return;

    log.info('Processing sync queue before migration', 'MIGRATION', {
      queueLength: syncQueue.length
    });

    for (const item of syncQueue) {
      if (item.userId === userId && item.operation === 'upload' && item.data) {
        try {
          await this.saveToFirestore(item.data, userId);
        } catch (error) {
          log.warn('Failed to process sync queue item', 'MIGRATION', {
            itemId: item.localChecklistId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }
  }

  /**
   * Migrate all checklists to Firestore
   */
  private static async migrateAllChecklists(userId: string): Promise<{
    migrated: number;
    skipped: number;
    errors: string[];
  }> {
    await this.updateMigrationStatus({
      phase: 'migrate'
    });

    const checklists = this.getLocalChecklists();
    const result = { migrated: 0, skipped: 0, errors: [] as string[] };
    
    if (checklists.length === 0) {
      log.info('No checklists to migrate', 'MIGRATION', {});
      return result;
    }

    await this.updateMigrationStatus({
      phase: 'migrate',
      progress: { total: checklists.length, completed: 0, failed: 0 }
    });

    // Process in batches to avoid overwhelming Firestore
    const batchSize = 50;
    for (let i = 0; i < checklists.length; i += batchSize) {
      const batch = checklists.slice(i, i + batchSize);
      
      try {
        const batchResult = await this.migrateBatch(batch, userId);
        result.migrated += batchResult.migrated;
        result.skipped += batchResult.skipped;
        result.errors.push(...batchResult.errors);

        // Update progress
        await this.updateMigrationStatus({
          phase: 'migrate',
          progress: { 
            total: checklists.length, 
            completed: Math.min(i + batchSize, checklists.length), 
            failed: result.errors.length 
          }
        });

      } catch (error) {
        const errorMsg = `Batch ${Math.floor(i / batchSize) + 1} failed: ${error}`;
        result.errors.push(errorMsg);
        log.error('Batch migration failed', 'MIGRATION', { batch: i / batchSize + 1, error });
      }
    }

    log.info('Checklist migration completed', 'MIGRATION', result);
    return result;
  }

  /**
   * Migrate a batch of checklists using Firestore batch operations
   */
  private static async migrateBatch(checklists: ChecklistData[], userId: string): Promise<{
    migrated: number;
    skipped: number;
    errors: string[];
  }> {
    const result = { migrated: 0, skipped: 0, errors: [] as string[] };
    const batch = writeBatch(db);

    for (const checklist of checklists) {
      try {
        // Check if already exists in Firestore
        const existsInFirestore = await this.checkIfExistsInFirestore(checklist.id, userId);
        if (existsInFirestore) {
          result.skipped++;
          continue;
        }

        // Prepare data for Firestore
        const firestoreData = this.prepareChecklistForFirestore(checklist, userId);
        const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklist.id);
        
        batch.set(docRef, firestoreData);
        result.migrated++;

      } catch (error) {
        result.errors.push(`Failed to prepare ${checklist.id}: ${error}`);
      }
    }

    // Commit batch if there are documents to migrate
    if (result.migrated > 0) {
      await batch.commit();
    }

    return result;
  }

  /**
   * Check if checklist already exists in Firestore
   */
  private static async checkIfExistsInFirestore(checklistId: string, userId: string): Promise<boolean> {
    try {
      const q = query(
        collection(db, this.CHECKLISTS_COLLECTION),
        where('originalLocalId', '==', checklistId),
        where('userId', '==', userId)
      );
      const snapshot = await getDocs(q);
      return !snapshot.empty;
    } catch (error) {
      return false;
    }
  }

  /**
   * Prepare checklist data for Firestore storage
   */
  private static prepareChecklistForFirestore(checklist: ChecklistData, userId: string): any {
    return {
      ...checklist,
      userId,
      originalLocalId: checklist.id,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      migratedAt: Timestamp.now(),
      migratedFrom: 'localStorage',
      // Remove custom sync metadata - Firestore handles this
      syncMetadata: undefined
    };
  }

  /**
   * Save individual checklist to Firestore
   */
  private static async saveToFirestore(checklist: ChecklistData, userId: string): Promise<void> {
    const firestoreData = this.prepareChecklistForFirestore(checklist, userId);
    const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklist.id);
    await setDoc(docRef, firestoreData, { merge: true });
  }

  /**
   * Validate migration by comparing data
   */
  private static async validateMigration(userId: string): Promise<{
    isValid: boolean;
    errors: string[];
  }> {
    await this.updateMigrationStatus({
      phase: 'validate'
    });

    const result = { isValid: true, errors: [] as string[] };

    try {
      const originalChecklists = this.getLocalChecklists();
      const migratedChecklists = await this.getFirestoreChecklists(userId);

      // Check counts
      if (originalChecklists.length !== migratedChecklists.length) {
        result.errors.push(
          `Count mismatch: ${originalChecklists.length} local vs ${migratedChecklists.length} Firestore`
        );
      }

      // Check individual checklists
      for (const original of originalChecklists) {
        const migrated = migratedChecklists.find(c => c.originalLocalId === original.id);
        if (!migrated) {
          result.errors.push(`Missing checklist in Firestore: ${original.id}`);
          continue;
        }

        // Validate critical fields
        if (original.equipmentTagId !== migrated.equipmentTagId) {
          result.errors.push(`Equipment ID mismatch for ${original.id}`);
        }

        if (original.isCompleted !== migrated.isCompleted) {
          result.errors.push(`Completion status mismatch for ${original.id}`);
        }
      }

      result.isValid = result.errors.length === 0;

      log.info('Migration validation completed', 'MIGRATION', {
        isValid: result.isValid,
        errorCount: result.errors.length
      });

    } catch (error) {
      result.errors.push(`Validation failed: ${error}`);
      result.isValid = false;
    }

    return result;
  }

  /**
   * Get checklists from Firestore for validation
   */
  private static async getFirestoreChecklists(userId: string): Promise<any[]> {
    const q = query(
      collection(db, this.CHECKLISTS_COLLECTION),
      where('userId', '==', userId)
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  /**
   * Complete migration and enable Firestore persistence
   */
  private static async completeMigration(): Promise<void> {
    await this.updateMigrationStatus({
      phase: 'complete'
    });

    // Enable Firestore persistence mode
    localStorage.setItem('firestore-persistence-enabled', 'true');
    localStorage.setItem('migration-completed-at', new Date().toISOString());
    
    // Clear sync queue as it's no longer needed
    localStorage.removeItem(this.SYNC_QUEUE_KEY);

    log.info('Migration completed and Firestore persistence enabled', 'MIGRATION', {});
  }

  /**
   * Rollback migration and restore original data
   */
  static async rollback(): Promise<{ success: boolean; error?: string }> {
    try {
      const backup = localStorage.getItem(this.BACKUP_DATA_KEY);
      if (!backup) {
        throw new Error('No backup data found for rollback');
      }

      const backupData: MigrationBackup = JSON.parse(backup);

      // Restore original data
      localStorage.setItem(this.ORIGINAL_STORAGE_KEY, JSON.stringify(backupData.checklists));
      localStorage.setItem(this.SYNC_QUEUE_KEY, JSON.stringify(backupData.syncQueue));
      localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(backupData.settings));

      // Disable Firestore persistence
      localStorage.removeItem('firestore-persistence-enabled');
      localStorage.removeItem('migration-completed-at');

      // Clear migration status
      localStorage.removeItem(this.MIGRATION_STATUS_KEY);

      log.info('Migration rollback completed successfully', 'MIGRATION', {
        restoredChecklists: backupData.checklists.length
      });

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Migration rollback failed', 'MIGRATION', { error: errorMessage });
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Check if migration is needed
   */
  static isMigrationNeeded(): boolean {
    const hasLocalData = localStorage.getItem(this.ORIGINAL_STORAGE_KEY);
    const migrationCompleted = localStorage.getItem('migration-completed-at');
    return !!hasLocalData && !migrationCompleted;
  }

  /**
   * Check if Firestore persistence is enabled
   */
  static isFirestorePersistenceEnabled(): boolean {
    return localStorage.getItem('firestore-persistence-enabled') === 'true';
  }

  /**
   * Get current migration status
   */
  static getMigrationStatus(): MigrationStatus | null {
    const status = localStorage.getItem(this.MIGRATION_STATUS_KEY);
    return status ? JSON.parse(status) : null;
  }

  /**
   * Update migration status
   */
  private static async updateMigrationStatus(updates: Partial<MigrationStatus>): Promise<void> {
    const current = this.getMigrationStatus() || {
      phase: 'backup',
      startedAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      progress: { total: 0, completed: 0, failed: 0 },
      errors: [],
      backupCreated: false
    };

    const updated = {
      ...current,
      ...updates,
      lastUpdated: new Date().toISOString()
    };

    localStorage.setItem(this.MIGRATION_STATUS_KEY, JSON.stringify(updated));
  }

  /**
   * Get current migration phase
   */
  private static getCurrentPhase(): string {
    const status = this.getMigrationStatus();
    return status?.phase || 'unknown';
  }

  /**
   * Helper methods for accessing localStorage data
   */
  private static getLocalChecklists(): ChecklistData[] {
    try {
      const data = localStorage.getItem(this.ORIGINAL_STORAGE_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      log.error('Failed to get local checklists', 'MIGRATION', error);
      return [];
    }
  }

  private static getSyncQueue(): any[] {
    try {
      const data = localStorage.getItem(this.SYNC_QUEUE_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      return [];
    }
  }

  private static getStorageSettings(): any {
    try {
      const data = localStorage.getItem(this.SETTINGS_KEY);
      return data ? JSON.parse(data) : {};
    } catch (error) {
      return {};
    }
  }
} 