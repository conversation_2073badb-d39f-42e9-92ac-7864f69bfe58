import { ChecklistData, ChecklistSummary, SyncMetadata, SyncStatus } from '@/types/checklist';
import { UserRole } from '@/types/user';
import { log } from './logger';
import { FirestoreStorageService, type SyncStatusInfo } from './firestore-storage';
import { MigrationService } from './migration-service';

/**
 * Storage Adapter - Provides backward compatibility while routing to Firestore
 * This maintains the same interface as the original StorageService
 */

// Export the existing storage utilities that don't need migration
export { calculateChecklistSummary, convertImageToBase64, getStorageData } from './storage';

// Re-export types for compatibility
export type { StorageSettings, StorageStatus } from './firestore-storage';

interface StorageInfo {
  currentSize: number;
  maxSize: number;
  usagePercentage: number;
  itemCount: number;
  canSave: boolean;
}

// Legacy compatibility interface
interface LegacyStorageSettings {
  lastCleanup: string;
  showStorageWarnings: boolean;
  maxStorageSize: number;
  daysToKeepCompleted: number;
  daysToKeepSynced: number;
}

export class StorageService {
  private static userId: string | null = null;
  private static initialized = false;

  /**
   * Initialize the storage service with user context
   */
  static async initialize(userId: string): Promise<void> {
    this.userId = userId;
    
    if (MigrationService.isFirestorePersistenceEnabled()) {
      await FirestoreStorageService.initialize(userId);
      this.initialized = true;
      log.info('Storage service initialized with Firestore', 'STORAGE', { userId });
    } else {
      // Check if migration is needed
      if (MigrationService.isMigrationNeeded()) {
        log.info('Migration needed but not triggered from storage service', 'STORAGE', { userId });
        // Let the UI handle migration prompting
      }
      this.initialized = true;
      log.info('Storage service initialized with localStorage fallback', 'STORAGE', { userId });
    }
  }

  /**
   * Check if storage service is properly initialized
   */
  static isInitialized(): boolean {
    return this.initialized && this.userId !== null;
  }

  /**
   * Get all checklists for the current user
   */
  static getAllChecklists(): ChecklistData[] {
    if (!this.ensureInitialized()) {
      return this.getLegacyChecklists();
    }

    if (MigrationService.isFirestorePersistenceEnabled()) {
      // For Firestore, we need to use async methods
      // This sync method returns cached data or empty array
      log.warn('getAllChecklists called synchronously on Firestore storage', 'STORAGE', {});
      return [];
    }

    return this.getLegacyChecklists();
  }

  /**
   * Get all checklists asynchronously
   */
  static async getAllChecklistsAsync(): Promise<ChecklistData[]> {
    if (!this.ensureInitialized()) {
      return this.getLegacyChecklists();
    }

    if (MigrationService.isFirestorePersistenceEnabled()) {
      return await FirestoreStorageService.getAllChecklists(this.userId!);
    }

    return this.getLegacyChecklists();
  }

  /**
   * Get a specific checklist by ID
   */
  static getChecklistById(id: string): ChecklistData | null {
    if (!this.ensureInitialized()) {
      return this.getLegacyChecklistById(id);
    }

    if (MigrationService.isFirestorePersistenceEnabled()) {
      // Sync method not supported for Firestore
      log.warn('getChecklistById called synchronously on Firestore storage', 'STORAGE', { id });
      return null;
    }

    return this.getLegacyChecklistById(id);
  }

  /**
   * Get a specific checklist by ID asynchronously
   */
  static async getChecklistByIdAsync(id: string): Promise<ChecklistData | null> {
    if (!this.ensureInitialized()) {
      return this.getLegacyChecklistById(id);
    }

    if (MigrationService.isFirestorePersistenceEnabled()) {
      return await FirestoreStorageService.getChecklist(id, this.userId!);
    }

    return this.getLegacyChecklistById(id);
  }

  /**
   * Save a checklist
   */
  static async saveChecklist(checklist: ChecklistData, userRole?: UserRole): Promise<boolean> {
    if (!this.ensureInitialized()) {
      return this.saveLegacyChecklist(checklist, userRole);
    }

    try {
      if (MigrationService.isFirestorePersistenceEnabled()) {
        await FirestoreStorageService.saveChecklist(checklist, this.userId!);
        return true;
      }

      return this.saveLegacyChecklist(checklist, userRole);

    } catch (error) {
      log.error('Failed to save checklist', 'STORAGE', {
        checklistId: checklist.id,
        userId: this.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Update checklist completion status
   */
  static async updateCompletionStatus(
    id: string, 
    isCompleted: boolean, 
    completedBy: string,
    userRole: UserRole
  ): Promise<boolean> {
    if (!this.ensureInitialized()) {
      return this.updateLegacyCompletionStatus(id, isCompleted, completedBy, userRole);
    }

    try {
      if (MigrationService.isFirestorePersistenceEnabled()) {
        const completedAt = isCompleted ? new Date().toISOString() : undefined;
        await FirestoreStorageService.updateChecklistStatus(id, this.userId!, isCompleted, completedAt);
        return true;
      }

      return this.updateLegacyCompletionStatus(id, isCompleted, completedBy, userRole);

    } catch (error) {
      log.error('Failed to update completion status', 'STORAGE', {
        checklistId: id,
        userId: this.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Delete a checklist
   */
  static async deleteChecklist(id: string): Promise<boolean> {
    if (!this.ensureInitialized()) {
      return this.deleteLegacyChecklist(id);
    }

    try {
      if (MigrationService.isFirestorePersistenceEnabled()) {
        await FirestoreStorageService.deleteChecklist(id, this.userId!);
        return true;
      }

      return this.deleteLegacyChecklist(id);

    } catch (error) {
      log.error('Failed to delete checklist', 'STORAGE', {
        checklistId: id,
        userId: this.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Subscribe to real-time checklist updates
   */
  static subscribeToChecklists(callback: (checklists: ChecklistData[]) => void): () => void {
    if (!this.ensureInitialized()) {
      // Return a no-op unsubscribe function for legacy
      return () => {};
    }

    if (MigrationService.isFirestorePersistenceEnabled()) {
      return FirestoreStorageService.subscribeToChecklists(this.userId!, callback);
    }

    // Legacy: Set up localStorage change listening
    return this.setupLegacySubscription(callback);
  }

  /**
   * Clear all user data
   */
  static async clearAllChecklists(): Promise<boolean> {
    if (!this.ensureInitialized()) {
      return this.clearLegacyChecklists();
    }

    try {
      if (MigrationService.isFirestorePersistenceEnabled()) {
        await FirestoreStorageService.clearUserData(this.userId!);
        return true;
      }

      return this.clearLegacyChecklists();

    } catch (error) {
      log.error('Failed to clear all checklists', 'STORAGE', {
        userId: this.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Generate a new unique ID
   */
  static generateId(): string {
    return crypto.randomUUID?.() || 
           `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create default sync metadata
   */
  static createDefaultSyncMetadata(): SyncMetadata {
    return {
      status: 'local-only',
      lastLocalUpdateAt: new Date().toISOString(),
      localVersion: 1
    };
  }

  /**
   * Get storage information and statistics
   */
  static async getStorageInfo(): Promise<StorageInfo> {
    if (!this.ensureInitialized()) {
      return this.getLegacyStorageInfo();
    }

    if (MigrationService.isFirestorePersistenceEnabled()) {
      const status = await FirestoreStorageService.getStorageStatus(this.userId!);
      return {
        currentSize: status.used,
        maxSize: status.quota,
        usagePercentage: (status.used / status.quota) * 100,
        itemCount: 0, // Would need separate query
        canSave: status.available
      };
    }

    return this.getLegacyStorageInfo();
  }

  /**
   * Get sync status
   */
  static getSyncStatus(): SyncStatusInfo {
    if (MigrationService.isFirestorePersistenceEnabled()) {
      return FirestoreStorageService.getSyncStatus();
    }

    // Legacy sync status
    return {
      inProgress: false,
      lastSync: null,
      hasChanges: false,
      isOnline: navigator.onLine,
      queueSize: 0
    };
  }

  /**
   * Bulk operations
   */
  static async bulkSaveChecklists(checklists: ChecklistData[]): Promise<void> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }

    if (MigrationService.isFirestorePersistenceEnabled()) {
      await FirestoreStorageService.bulkSaveChecklists(checklists, this.userId!);
    } else {
      // Legacy bulk save
      for (const checklist of checklists) {
        await this.saveChecklist(checklist);
      }
    }
  }

  /**
   * Perform cleanup of old checklists
   */
  static async performCleanup(): Promise<{ cleaned: number; errors: string[] }> {
    if (!this.ensureInitialized()) {
      return { cleaned: 0, errors: ['Storage service not initialized'] };
    }

    try {
      if (MigrationService.isFirestorePersistenceEnabled()) {
        // For Firestore, we can implement cleanup based on settings
        // For now, just return that no cleanup was needed
        return { cleaned: 0, errors: [] };
      } else {
        // Legacy cleanup - remove old completed checklists
        const checklists = this.getLegacyChecklists();
        const now = Date.now();
        const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000); // 30 days

        const toRemove = checklists.filter(checklist => {
          if (!checklist.isCompleted) return false;
          const completedAt = checklist.completedAt ? new Date(checklist.completedAt).getTime() : 0;
          return completedAt < thirtyDaysAgo;
        });

        // Remove old completed checklists
        const remaining = checklists.filter(checklist => !toRemove.includes(checklist));
        localStorage.setItem('ppm-checklists', JSON.stringify(remaining));
        this.dispatchChecklistsUpdated();

        return { cleaned: toRemove.length, errors: [] };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { cleaned: 0, errors: [errorMessage] };
    }
  }

  /**
   * Network status methods
   */
  static getNetworkStatus(): { isOnline: boolean; lastSync?: string } {
    if (MigrationService.isFirestorePersistenceEnabled()) {
      return FirestoreStorageService.getNetworkStatus();
    }

    return {
      isOnline: navigator.onLine
    };
  }

  // Private helper methods

  private static ensureInitialized(): boolean {
    if (!this.initialized || !this.userId) {
      log.warn('Storage service not initialized, falling back to legacy', 'STORAGE', {});
      return false;
    }
    return true;
  }

  private static getLegacyChecklists(): ChecklistData[] {
    try {
      const data = localStorage.getItem('ppm-checklists');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      log.error('Failed to get legacy checklists', 'STORAGE', { error });
      return [];
    }
  }

  private static getLegacyChecklistById(id: string): ChecklistData | null {
    const checklists = this.getLegacyChecklists();
    return checklists.find(c => c.id === id) || null;
  }

  private static saveLegacyChecklist(checklist: ChecklistData, userRole?: UserRole): boolean {
    try {
      const checklists = this.getLegacyChecklists();
      const existingIndex = checklists.findIndex(c => c.id === checklist.id);
      
      if (existingIndex >= 0) {
        checklists[existingIndex] = checklist;
      } else {
        checklists.push(checklist);
      }

      localStorage.setItem('ppm-checklists', JSON.stringify(checklists));
      this.dispatchChecklistsUpdated();
      return true;
    } catch (error) {
      log.error('Failed to save legacy checklist', 'STORAGE', { error });
      return false;
    }
  }

  private static updateLegacyCompletionStatus(
    id: string, 
    isCompleted: boolean, 
    completedBy: string,
    userRole: UserRole
  ): boolean {
    try {
      const checklist = this.getLegacyChecklistById(id);
      if (!checklist) return false;

      checklist.isCompleted = isCompleted;
      checklist.completedAt = isCompleted ? new Date().toISOString() : undefined;
      checklist.completedBy = isCompleted ? completedBy : undefined;
      checklist.updatedAt = new Date().toISOString();

      return this.saveLegacyChecklist(checklist, userRole);
    } catch (error) {
      log.error('Failed to update legacy completion status', 'STORAGE', { error });
      return false;
    }
  }

  private static deleteLegacyChecklist(id: string): boolean {
    try {
      const checklists = this.getLegacyChecklists();
      const filtered = checklists.filter(c => c.id !== id);
      
      localStorage.setItem('ppm-checklists', JSON.stringify(filtered));
      this.dispatchChecklistsUpdated();
      return true;
    } catch (error) {
      log.error('Failed to delete legacy checklist', 'STORAGE', { error });
      return false;
    }
  }

  private static clearLegacyChecklists(): boolean {
    try {
      localStorage.removeItem('ppm-checklists');
      this.dispatchChecklistsUpdated();
      return true;
    } catch (error) {
      log.error('Failed to clear legacy checklists', 'STORAGE', { error });
      return false;
    }
  }

  private static getLegacyStorageInfo(): StorageInfo {
    try {
      const data = localStorage.getItem('ppm-checklists');
      const currentSize = data ? new Blob([data]).size : 0;
      const maxSize = 4 * 1024 * 1024; // 4MB
      const checklists = data ? JSON.parse(data) : [];
      
      return {
        currentSize,
        maxSize,
        usagePercentage: (currentSize / maxSize) * 100,
        itemCount: checklists.length,
        canSave: currentSize < maxSize * 0.8
      };
    } catch (error) {
      return {
        currentSize: 0,
        maxSize: 4 * 1024 * 1024,
        usagePercentage: 0,
        itemCount: 0,
        canSave: true
      };
    }
  }

  private static setupLegacySubscription(callback: (checklists: ChecklistData[]) => void): () => void {
    const handler = () => callback(this.getLegacyChecklists());
    
    // Initial call
    handler();
    
    // Listen for custom events
    window.addEventListener('checklistsUpdated', handler);
    
    // Return unsubscribe function
    return () => {
      window.removeEventListener('checklistsUpdated', handler);
    };
  }

  private static dispatchChecklistsUpdated(): void {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('checklistsUpdated'));
    }
  }
} 