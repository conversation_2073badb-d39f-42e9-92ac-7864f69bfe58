import { ChecklistData, ChecklistSummary, SyncMetadata, SyncStatus } from '@/types/checklist';
import { UserRole } from '@/types/user';
import { log } from './logger';

const STORAGE_KEY = 'ppm-checklists';
const STORAGE_SETTINGS_KEY = 'ppm-storage-settings';

// Storage limits and settings
const MAX_STORAGE_SIZE_MB = 4; // 4MB limit (localStorage typically has 5-10MB)
const MAX_CHECKLISTS_COUNT = 500; // Maximum number of checklists to keep
const STORAGE_WARNING_THRESHOLD = 0.8; // Show warning when 80% full
const STORAGE_CRITICAL_THRESHOLD = 0.9; // Show critical warning when 90% full
const DAYS_TO_KEEP_COMPLETED = 30; // Days to keep completed checklists
const DAYS_TO_KEEP_SYNCED = 7; // Days to keep synced checklists

// Rate limiting for event dispatching
let lastEventTime = 0;
const EVENT_THROTTLE_MS = 100; // Minimum time between events

// Storage settings interface
interface StorageSettings {
  lastCleanup: string;
  showStorageWarnings: boolean;
  maxStorageSize: number;
  daysToKeepCompleted: number;
  daysToKeepSynced: number;
}

// Storage size monitoring
interface StorageInfo {
  currentSize: number;
  maxSize: number;
  usagePercentage: number;
  itemCount: number;
  canSave: boolean;
}

// Utility function to dispatch custom events for real-time updates with rate limiting
const dispatchChecklistsUpdated = () => {
  if (typeof window !== 'undefined') {
    const now = Date.now();
    if (now - lastEventTime >= EVENT_THROTTLE_MS) {
      lastEventTime = now;
      window.dispatchEvent(new CustomEvent('checklistsUpdated'));
    }
  }
};

// Utility function to check if user has admin privileges
const isUserAdmin = (userRole?: UserRole): boolean => {
  return userRole === UserRole.ADMIN;
};

// Storage management utilities
class StorageManager {
  static getStorageSettings(): StorageSettings {
    try {
      const settings = localStorage.getItem(STORAGE_SETTINGS_KEY);
      if (settings) {
        return JSON.parse(settings);
      }
    } catch (error) {
      console.warn('Failed to load storage settings:', error);
    }
    
    // Default settings
    return {
      lastCleanup: new Date().toISOString(),
      showStorageWarnings: true,
      maxStorageSize: MAX_STORAGE_SIZE_MB * 1024 * 1024, // Convert to bytes
      daysToKeepCompleted: DAYS_TO_KEEP_COMPLETED,
      daysToKeepSynced: DAYS_TO_KEEP_SYNCED
    };
  }

  static saveStorageSettings(settings: StorageSettings): void {
    try {
      localStorage.setItem(STORAGE_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.warn('Failed to save storage settings:', error);
    }
  }

  static getStorageInfo(): StorageInfo {
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      const currentSize = data ? new Blob([data]).size : 0;
      const settings = this.getStorageSettings();
      const checklists = data ? JSON.parse(data) : [];
      
      return {
        currentSize,
        maxSize: settings.maxStorageSize,
        usagePercentage: (currentSize / settings.maxStorageSize) * 100,
        itemCount: checklists.length,
        canSave: currentSize < settings.maxStorageSize * STORAGE_WARNING_THRESHOLD
      };
    } catch (error) {
      console.warn('Failed to get storage info:', error);
      return {
        currentSize: 0,
        maxSize: MAX_STORAGE_SIZE_MB * 1024 * 1024,
        usagePercentage: 0,
        itemCount: 0,
        canSave: true
      };
    }
  }

  static isQuotaExceededError(error: any): boolean {
    return error && (
      error.name === 'QuotaExceededError' ||
      error.code === 22 ||
      error.message?.includes('quota') ||
      error.message?.includes('exceeded') ||
      error.message?.includes('storage')
    );
  }

  static async performAutoCleanup(): Promise<{ cleaned: number; errors: string[] }> {
    const results = { cleaned: 0, errors: [] as string[] };
    
    try {
      const checklists = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
      const settings = this.getStorageSettings();
      const now = new Date();
      const completedCutoff = new Date(now.getTime() - settings.daysToKeepCompleted * 24 * 60 * 60 * 1000);
      const syncedCutoff = new Date(now.getTime() - settings.daysToKeepSynced * 24 * 60 * 60 * 1000);

      const checklistsToKeep = checklists.filter((checklist: ChecklistData) => {
        // Always keep incomplete and local-only checklists
        if (!checklist.isCompleted || checklist.syncMetadata.status === 'local-only') {
          return true;
        }

        // Remove old completed checklists
        if (checklist.isCompleted && checklist.completedAt) {
          const completedDate = new Date(checklist.completedAt);
          if (completedDate < completedCutoff) {
            results.cleaned++;
            return false;
          }
        }

        // Remove old synced checklists
        if (checklist.syncMetadata.status === 'synced' && checklist.syncMetadata.lastSyncedAt) {
          const syncedDate = new Date(checklist.syncMetadata.lastSyncedAt);
          if (syncedDate < syncedCutoff) {
            results.cleaned++;
            return false;
          }
        }

        return true;
      });

      // If still too many, remove oldest completed and synced items
      if (checklistsToKeep.length > MAX_CHECKLISTS_COUNT) {
        const sortedChecklists = checklistsToKeep.sort((a: ChecklistData, b: ChecklistData) => {
          // Prioritize keeping incomplete and local-only
          if (!a.isCompleted && b.isCompleted) return -1;
          if (a.isCompleted && !b.isCompleted) return 1;
          if (a.syncMetadata.status === 'local-only' && b.syncMetadata.status !== 'local-only') return -1;
          if (a.syncMetadata.status !== 'local-only' && b.syncMetadata.status === 'local-only') return 1;
          
          // Then sort by date (newest first)
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        });

        const finalChecklists = sortedChecklists.slice(0, MAX_CHECKLISTS_COUNT);
        results.cleaned += checklistsToKeep.length - finalChecklists.length;
        
        localStorage.setItem(STORAGE_KEY, JSON.stringify(finalChecklists));
      } else {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(checklistsToKeep));
      }

      // Update cleanup timestamp
      settings.lastCleanup = now.toISOString();
      this.saveStorageSettings(settings);

      log.info('Auto cleanup completed', 'STORAGE', { 
        cleaned: results.cleaned, 
        remaining: checklistsToKeep.length 
      });

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown cleanup error';
      results.errors.push(errorMsg);
      console.error('Auto cleanup failed:', error);
    }

    return results;
  }

  static async safeSetItem(key: string, value: string): Promise<boolean> {
    try {
      // Check storage info before attempting to save
      const storageInfo = this.getStorageInfo();
      const newDataSize = new Blob([value]).size;
      
      // Warn user if approaching storage limits but don't auto-cleanup
      if ((storageInfo.currentSize + newDataSize) > storageInfo.maxSize * STORAGE_WARNING_THRESHOLD) {
        this.notifyStorageWarning(storageInfo.usagePercentage);
      }

      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      if (this.isQuotaExceededError(error)) {
        console.error('Storage quota exceeded - user action required');
        
        // Notify user that action is required - no automatic cleanup
        this.notifyStorageQuotaExceeded();
        return false;
      } else {
        console.error('Failed to save to localStorage:', error);
        return false;
      }
    }
  }

  static async performEmergencyCleanup(): Promise<void> {
    try {
      const checklists = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
      
      // Keep only the most recent incomplete checklists and critical synced items
      const criticalChecklists = checklists
        .filter((checklist: ChecklistData) => 
          !checklist.isCompleted || checklist.syncMetadata.status === 'local-only'
        )
        .sort((a: ChecklistData, b: ChecklistData) => 
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        )
        .slice(0, 50); // Keep only 50 most recent critical items

      localStorage.setItem(STORAGE_KEY, JSON.stringify(criticalChecklists));
      
      log.warn('Emergency cleanup performed', 'STORAGE', { 
        original: checklists.length, 
        remaining: criticalChecklists.length 
      });
    } catch (error) {
      console.error('Emergency cleanup failed:', error);
      throw error;
    }
  }

  static notifyStorageWarning(usagePercentage: number): void {
    // Dispatch a custom event for storage warnings
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('storageWarning', {
        detail: {
          message: `Storage usage is high (${usagePercentage.toFixed(1)}%). Consider cleaning up old data or syncing to cloud.`,
          usagePercentage,
          action: 'storage-warning'
        }
      }));
    }
  }

  static notifyStorageQuotaExceeded(): void {
    // Dispatch a custom event for components to handle
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('storageQuotaExceeded', {
        detail: {
          message: 'Storage quota exceeded! Please manually clean up data or sync to cloud to continue.',
          action: 'quota-exceeded'
        }
      }));
    }
  }
}

export class StorageService {
  static getAllChecklists(): ChecklistData[] {
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      if (!data) {
        log.storage('getAllChecklists', 'success', { count: 0 });
        return [];
      }
      
      const checklists = JSON.parse(data);
      
      // Migrate old data that doesn't have completion fields
      let needsMigration = false;
      const migratedChecklists = checklists.map((checklist: any) => {
        if (checklist.isCompleted === undefined) {
          needsMigration = true;
          return {
            ...checklist,
            isCompleted: false, // Default to not completed for existing data
            // Don't set completedAt or completedBy for existing data
          };
        }
        return checklist;
      });
      
      // Save migrated data if needed
      if (needsMigration) {
        StorageManager.safeSetItem(STORAGE_KEY, JSON.stringify(migratedChecklists));
        log.info('Migrated checklist data to include completion fields', 'STORAGE', { count: migratedChecklists.length });
      }
      
      log.storage('getAllChecklists', 'success', { count: migratedChecklists.length });
      return migratedChecklists;
    } catch (error) {
      log.storage('getAllChecklists', 'failure', { error });
      console.error('Error reading from localStorage:', error);
      return [];
    }
  }

  static getChecklistById(id: string): ChecklistData | null {
    try {
      const checklists = this.getAllChecklists();
      const checklist = checklists.find(checklist => checklist.id === id) || null;
      log.storage('getChecklistById', 'success', { id, found: !!checklist });
      return checklist;
    } catch (error) {
      log.storage('getChecklistById', 'failure', { id, error });
      return null;
    }
  }

  static async saveChecklist(checklist: ChecklistData, userRole?: UserRole): Promise<boolean> {
    try {
      const checklists = this.getAllChecklists();
      const existingIndex = checklists.findIndex(c => c.id === checklist.id);

      // Update sync metadata
      const now = new Date().toISOString();
      if (existingIndex >= 0) {
        const existingChecklist = checklists[existingIndex];
        
        // Check if user is trying to modify completion status without admin rights
        const isModifyingCompletion = checklist.isCompleted !== existingChecklist.isCompleted;
        if (isModifyingCompletion && !isUserAdmin(userRole)) {
          log.warn('Non-admin user attempted to modify completion status', 'STORAGE', { 
            checklistId: checklist.id, 
            userRole 
          });
          // Preserve existing completion status
          checklist.isCompleted = existingChecklist.isCompleted;
          checklist.completedAt = existingChecklist.completedAt;
          checklist.completedBy = existingChecklist.completedBy;
        }
        
        // Preserve important sync metadata from existing checklist
        const preservedSyncMetadata = {
          cloudDocumentId: existingChecklist.syncMetadata.cloudDocumentId,
          cloudVersion: existingChecklist.syncMetadata.cloudVersion,
        };

        // Update existing checklist
        const updated = { 
          ...checklist, 
          updatedAt: now,
          syncMetadata: {
            ...checklist.syncMetadata,
            ...preservedSyncMetadata, // Ensure we don't lose cloud references
            lastLocalUpdateAt: now,
            localVersion: (checklist.syncMetadata.localVersion || existingChecklist.syncMetadata.localVersion || 0) + 1,
            // Only change status if it's not a conflict or error state
            status: this.determineNewSyncStatus(existingChecklist.syncMetadata.status, checklist.syncMetadata.status)
          }
        };
        checklists[existingIndex] = updated;
        log.storage('saveChecklist', 'success', { 
          id: checklist.id, 
          operation: 'update',
          version: updated.syncMetadata.localVersion,
          oldStatus: existingChecklist.syncMetadata.status,
          newStatus: updated.syncMetadata.status
        });
      } else {
        // New checklist - ensure proper defaults
        const newChecklist = {
          ...checklist,
          isCompleted: false, // New checklists are not completed
          completedAt: undefined,
          completedBy: undefined,
          syncMetadata: checklist.syncMetadata || this.createDefaultSyncMetadata()
        };
        checklists.push(newChecklist);
        log.storage('saveChecklist', 'success', { 
          id: checklist.id, 
          operation: 'create' 
        });
      }

      const success = await StorageManager.safeSetItem(STORAGE_KEY, JSON.stringify(checklists));
      
      if (success) {
        // Dispatch custom event for real-time updates
        dispatchChecklistsUpdated();
      } else {
        log.storage('saveChecklist', 'failure', { 
          id: checklist.id, 
          error: 'Storage quota exceeded' 
        });
      }
      
      return success;
    } catch (error) {
      log.storage('saveChecklist', 'failure', { id: checklist.id, error });
      console.error('Error saving to localStorage:', error);
      return false;
    }
  }

  /**
   * Determine new sync status based on current status and context
   */
  private static determineNewSyncStatus(currentStatus: SyncStatus, incomingStatus?: SyncStatus): SyncStatus {
    // If incoming status is explicitly set, use it (for cloud downloads)
    if (incomingStatus && incomingStatus !== currentStatus) {
      return incomingStatus;
    }

    // Don't change conflict or error states unless explicitly overridden
    if (currentStatus === 'conflict' || currentStatus === 'error') {
      return currentStatus;
    }

    // If currently synced, mark as pending for next sync
    if (currentStatus === 'synced') {
      return 'pending';
    }

    // Keep other statuses as is
    return currentStatus;
  }

  /**
   * Update completion status - admin only operation
   */
  static async updateCompletionStatus(
    id: string, 
    isCompleted: boolean, 
    completedBy: string,
    userRole: UserRole
  ): Promise<boolean> {
    if (!isUserAdmin(userRole)) {
      log.error('Unauthorized attempt to update completion status', 'STORAGE', { 
        id, 
        userRole, 
        attemptedBy: completedBy 
      });
      console.error('Unauthorized attempt to update completion status');
      return false;
    }

    try {
      const checklists = this.getAllChecklists();
      const checklistIndex = checklists.findIndex(c => c.id === id);
      
      if (checklistIndex >= 0) {
        const now = new Date().toISOString();
        checklists[checklistIndex].isCompleted = isCompleted;
        checklists[checklistIndex].completedAt = isCompleted ? now : undefined;
        checklists[checklistIndex].completedBy = isCompleted ? completedBy : undefined;
        checklists[checklistIndex].updatedAt = now;
        
        // Update sync status to pending if it was synced
        if (checklists[checklistIndex].syncMetadata.status === 'synced') {
          checklists[checklistIndex].syncMetadata.status = 'pending';
        }
        
        const success = await StorageManager.safeSetItem(STORAGE_KEY, JSON.stringify(checklists));
        
        if (success) {
          dispatchChecklistsUpdated();
          log.storage('updateCompletionStatus', 'success', { 
            id, 
            isCompleted, 
            completedBy 
          });
        } else {
          log.storage('updateCompletionStatus', 'failure', { 
            id, 
            error: 'Storage quota exceeded' 
          });
        }
        
        return success;
      }
      
      log.storage('updateCompletionStatus', 'failure', { 
        id, 
        error: 'Checklist not found' 
      });
      return false;
    } catch (error) {
      log.storage('updateCompletionStatus', 'failure', { id, error });
      console.error('Error updating completion status:', error);
      return false;
    }
  }

  static async deleteChecklist(id: string): Promise<boolean> {
    try {
      const checklists = this.getAllChecklists();
      const filteredChecklists = checklists.filter(checklist => checklist.id !== id);
      const success = await StorageManager.safeSetItem(STORAGE_KEY, JSON.stringify(filteredChecklists));
      
      if (success) {
        // Dispatch custom event for real-time updates
        dispatchChecklistsUpdated();
      }
      
      return success;
    } catch (error) {
      console.error('Error deleting from localStorage:', error);
      return false;
    }
  }

  static clearAllChecklists(): boolean {
    try {
      localStorage.removeItem(STORAGE_KEY);
      
      // Dispatch custom event for real-time updates
      dispatchChecklistsUpdated();
      
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  }

  static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
  }

  static createDefaultSyncMetadata(): SyncMetadata {
    const now = new Date().toISOString();
    return {
      status: 'local-only',
      lastLocalUpdateAt: now,
      localVersion: 1
    };
  }

  static async updateSyncMetadata(id: string, metadata: Partial<SyncMetadata>): Promise<boolean> {
    try {
      const checklists = this.getAllChecklists();
      const checklistIndex = checklists.findIndex(c => c.id === id);
      
      if (checklistIndex >= 0) {
        checklists[checklistIndex].syncMetadata = {
          ...checklists[checklistIndex].syncMetadata,
          ...metadata
        };
        const success = await StorageManager.safeSetItem(STORAGE_KEY, JSON.stringify(checklists));
        
        if (success) {
          // Dispatch custom event for real-time updates
          dispatchChecklistsUpdated();
        }
        
        return success;
      }
      return false;
    } catch (error) {
      console.error('Error updating sync metadata:', error);
      return false;
    }
  }

  static getChecklistsByUserId(userId: string): ChecklistData[] {
    return this.getAllChecklists().filter(checklist => checklist.userId === userId);
  }

  static getChecklistsBySyncStatus(status: string): ChecklistData[] {
    return this.getAllChecklists().filter(checklist => checklist.syncMetadata.status === status);
  }

  // Storage management methods
  static getStorageInfo(): StorageInfo {
    return StorageManager.getStorageInfo();
  }

  static getStorageSettings(): StorageSettings {
    return StorageManager.getStorageSettings();
  }

  static saveStorageSettings(settings: StorageSettings): void {
    return StorageManager.saveStorageSettings(settings);
  }

  static async performCleanup(): Promise<{ cleaned: number; errors: string[] }> {
    return await StorageManager.performAutoCleanup();
  }

  // Note: Emergency cleanup removed - all cleanup now requires user confirmation

  // Bulk operations for better storage management
  static async bulkDeleteChecklists(ids: string[]): Promise<{ success: string[]; failed: string[] }> {
    const results = { success: [] as string[], failed: [] as string[] };
    
    try {
      const checklists = this.getAllChecklists();
      const filteredChecklists = checklists.filter(checklist => {
        if (ids.includes(checklist.id)) {
          results.success.push(checklist.id);
          return false;
        }
        return true;
      });

      const success = await StorageManager.safeSetItem(STORAGE_KEY, JSON.stringify(filteredChecklists));
      
      if (success) {
        dispatchChecklistsUpdated();
      } else {
        // If save failed, mark all as failed
        results.failed = [...results.success];
        results.success = [];
      }
    } catch (error) {
      console.error('Bulk delete failed:', error);
      results.failed = ids;
      results.success = [];
    }

    return results;
  }

  static async optimizeStorage(): Promise<{ 
    originalSize: number; 
    newSize: number; 
    optimized: boolean; 
    compressionRatio: number;
  }> {
    try {
      const checklists = this.getAllChecklists();
      const originalData = JSON.stringify(checklists);
      const originalSize = new Blob([originalData]).size;

      // Remove unnecessary fields and optimize structure
      const optimizedChecklists = checklists.map(checklist => {
        const optimized = { ...checklist };
        
        // Remove empty or undefined fields
        Object.keys(optimized).forEach(key => {
          if (optimized[key as keyof ChecklistData] === undefined || 
              optimized[key as keyof ChecklistData] === null ||
              optimized[key as keyof ChecklistData] === '') {
            delete optimized[key as keyof ChecklistData];
          }
        });

        return optimized;
      });

      const optimizedData = JSON.stringify(optimizedChecklists);
      const newSize = new Blob([optimizedData]).size;
      const compressionRatio = originalSize > 0 ? (originalSize - newSize) / originalSize : 0;

      if (compressionRatio > 0.1) { // Only apply if we save more than 10%
        const success = await StorageManager.safeSetItem(STORAGE_KEY, optimizedData);
        
        if (success) {
          dispatchChecklistsUpdated();
          return {
            originalSize,
            newSize,
            optimized: true,
            compressionRatio
          };
        }
      }

      return {
        originalSize,
        newSize: originalSize,
        optimized: false,
        compressionRatio: 0
      };
    } catch (error) {
      console.error('Storage optimization failed:', error);
      return {
        originalSize: 0,
        newSize: 0,
        optimized: false,
        compressionRatio: 0
      };
    }
  }

  /**
   * Mark checklist as ready to submit - allows technician to control when work gets uploaded
   */
  static async markAsReadyToSubmit(id: string): Promise<boolean> {
    try {
      const checklists = this.getAllChecklists();
      const checklistIndex = checklists.findIndex(c => c.id === id);
      
      if (checklistIndex === -1) {
        log.storage('markAsReadyToSubmit', 'failure', { id, error: 'Checklist not found' });
        return false;
      }

      const checklist = checklists[checklistIndex];
      
      // Only allow marking local-only checklists as ready
      if (checklist.syncMetadata.status !== 'local-only') {
        log.storage('markAsReadyToSubmit', 'failure', { 
          id, 
          currentStatus: checklist.syncMetadata.status,
          error: 'Can only mark local-only checklists as ready' 
        });
        return false;
      }

      // Update sync metadata
      const updatedChecklist = {
        ...checklist,
        syncMetadata: {
          ...checklist.syncMetadata,
          status: 'ready-to-submit' as SyncStatus,
          lastLocalUpdateAt: new Date().toISOString(),
          localVersion: (checklist.syncMetadata.localVersion || 0) + 1
        }
      };

      checklists[checklistIndex] = updatedChecklist;
      
      const success = await StorageManager.safeSetItem(STORAGE_KEY, JSON.stringify(checklists));
      
      if (success) {
        dispatchChecklistsUpdated();
        log.storage('markAsReadyToSubmit', 'success', { id });
      } else {
        log.storage('markAsReadyToSubmit', 'failure', { id, error: 'Storage quota exceeded' });
      }
      
      return success;
    } catch (error) {
      log.storage('markAsReadyToSubmit', 'failure', { id, error });
      return false;
    }
  }

  /**
   * Unmark checklist as ready to submit - return to local-only status
   */
  static async unmarkAsReadyToSubmit(id: string): Promise<boolean> {
    try {
      const checklists = this.getAllChecklists();
      const checklistIndex = checklists.findIndex(c => c.id === id);
      
      if (checklistIndex === -1) {
        return false;
      }

      const checklist = checklists[checklistIndex];
      
      // Only allow unmarking ready-to-submit checklists
      if (checklist.syncMetadata.status !== 'ready-to-submit') {
        return false;
      }

      // Update sync metadata
      const updatedChecklist = {
        ...checklist,
        syncMetadata: {
          ...checklist.syncMetadata,
          status: 'local-only' as SyncStatus,
          lastLocalUpdateAt: new Date().toISOString()
        }
      };

      checklists[checklistIndex] = updatedChecklist;
      
      const success = await StorageManager.safeSetItem(STORAGE_KEY, JSON.stringify(checklists));
      
      if (success) {
        dispatchChecklistsUpdated();
        log.storage('unmarkAsReadyToSubmit', 'success', { id });
      }
      
      return success;
    } catch (error) {
      log.storage('unmarkAsReadyToSubmit', 'failure', { id, error });
      return false;
    }
  }
}

export function calculateChecklistSummary(checklist: ChecklistData): ChecklistSummary {
  const checks = {
    ...checklist.mechanicalChecks,
    ...checklist.electricalChecks,
    ...checklist.sequenceControlsChecks,
  };

  let totalOk = 0;
  let totalFaulty = 0;
  let totalNA = 0;
  let totalMissing = 0;
  let totalChecks = 0;

  Object.entries(checks).forEach(([, value]) => {
    // Skip numeric values (airflowVelocity, speedRpm, etc.)
    if (typeof value === 'string') {
      totalChecks++;
      switch (value) {
        case 'OK':
          totalOk++;
          break;
        case 'Faulty':
          totalFaulty++;
          break;
        case 'N/A':
          totalNA++;
          break;
        case 'Missing':
          totalMissing++;
          break;
      }
    }
  });

  return {
    totalOk,
    totalFaulty,
    totalNA,
    totalMissing,
    totalChecks,
  };
}

/**
 * @deprecated Use compressImage from image-compression.ts for better performance
 * This function is kept for backward compatibility
 */
export function convertImageToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert image to base64'));
      }
    };
    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
}

export const getStorageData = <T>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') return defaultValue;
  
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch {
    return defaultValue;
  }
};

// Export types for use in components
export type { StorageSettings, StorageInfo }; 