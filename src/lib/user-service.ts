import { httpsCallable } from 'firebase/functions';
import { doc, getDoc, updateDoc, Timestamp, setDoc, collection, query, orderBy, getDocs } from 'firebase/firestore';
import { db, functions } from '@/config/firebase-persistence';
import { UserDocument, UserRole, ExtendedUser } from '@/types/user';
import { AuthUser } from '@/types/auth';

// Type for handling various timestamp formats from Firestore
type FirestoreTimestamp = Timestamp | { seconds: number; nanoseconds?: number } | { _seconds: number; _nanoseconds?: number } | string | null | undefined;

/**
 * Service for handling user-related operations
 */
export class UserService {
  /**
   * Safely convert various timestamp formats to ISO string
   */
  private static processTimestamp(timestamp: FirestoreTimestamp): string | null {
    if (!timestamp) return null;
    
    // Handle Firestore Timestamp instances
    if (timestamp instanceof Timestamp) {
      return timestamp.toDate().toISOString();
    }
    
    // Handle timestamp objects (serialized from Firestore)
    if (typeof timestamp === 'object') {
      // Standard Firestore timestamp object
      if ('seconds' in timestamp && typeof timestamp.seconds === 'number') {
        return new Date(timestamp.seconds * 1000).toISOString();
      }
      // Alternative timestamp format
      if ('_seconds' in timestamp && typeof timestamp._seconds === 'number') {
        return new Date(timestamp._seconds * 1000).toISOString();
      }
    }
    
    // Handle string timestamps
    if (typeof timestamp === 'string') {
      const date = new Date(timestamp);
      return isNaN(date.getTime()) ? null : date.toISOString();
    }
    
    console.warn('Unknown timestamp format:', timestamp);
    return null;
  }

  /**
   * Get user profile data from Firestore
   */
  static async getUserProfile(uid: string): Promise<UserDocument | null> {
    try {
      const userDocRef = doc(db, 'users', uid);
      const userDoc = await getDoc(userDocRef);
      
      if (userDoc.exists()) {
        const data = userDoc.data();
        
        // Convert Firestore timestamps to strings for consistent handling
        const userData: UserDocument = {
          uid: data.uid,
          email: data.email,
          displayName: data.displayName,
          photoURL: data.photoURL,
          role: data.role,
          isActive: data.isActive,
          createdAt: this.processTimestamp(data.createdAt) || new Date().toISOString(),
          updatedAt: this.processTimestamp(data.updatedAt) || new Date().toISOString(),
          metadata: {
            lastLoginAt: this.processTimestamp(data.metadata?.lastLoginAt),
            emailVerified: data.metadata?.emailVerified || false,
            creationMethod: data.metadata?.creationMethod || 'unknown'
          }
        };
        
        return userData;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  }

  /**
   * Combine Firebase Auth user with Firestore user document
   */
  static async getExtendedUser(authUser: AuthUser): Promise<ExtendedUser | null> {
    try {
      const firestoreUser = await this.getUserProfile(authUser.uid);
      
      if (!firestoreUser) {
        return null;
      }

      return {
        // Firebase Auth data
        uid: authUser.uid,
        email: authUser.email,
        displayName: authUser.displayName,
        photoURL: authUser.photoURL,
        emailVerified: authUser.emailVerified,
        
        // Firestore data - timestamps are guaranteed to be strings from getUserProfile
        role: firestoreUser.role,
        isActive: firestoreUser.isActive,
        createdAt: firestoreUser.createdAt as string,
        updatedAt: firestoreUser.updatedAt as string,
        metadata: {
          lastLoginAt: firestoreUser.metadata.lastLoginAt as string | null,
          emailVerified: firestoreUser.metadata.emailVerified,
          creationMethod: firestoreUser.metadata.creationMethod
        }
      };
    } catch (error) {
      console.error('Error creating extended user:', error);
      throw error;
    }
  }

  /**
   * Update user profile (non-role fields only)
   */
  static async updateUserProfile(uid: string, updates: Partial<Pick<UserDocument, 'displayName' | 'photoURL'>>): Promise<void> {
    try {
      const userDocRef = doc(db, 'users', uid);
      
      await updateDoc(userDocRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  /**
   * Update user role (admin only - calls Firebase Function)
   */
  static async updateUserRole(targetUserId: string, newRole: UserRole): Promise<{ success: boolean; message: string }> {
    try {
      const updateRoleFunction = httpsCallable(functions, 'updateUserRole');
      const result = await updateRoleFunction({ targetUserId, newRole });
      return result.data as { success: boolean; message: string };
    } catch (error) {
      console.error('Error updating user role:', error);
      throw error;
    }
  }

  /**
   * Get user profile using Firebase Function (alternative method)
   */
  static async getUserProfileViaFunction(): Promise<UserDocument> {
    try {
      const getUserProfileFunction = httpsCallable(functions, 'getUserProfile');
      const result = await getUserProfileFunction();
      return result.data as UserDocument;
    } catch (error) {
      console.error('Error getting user profile via function:', error);
      throw error;
    }
  }

  /**
   * Check if a user document exists
   */
  static async userDocumentExists(uid: string): Promise<boolean> {
    try {
      const userDocRef = doc(db, 'users', uid);
      const userDoc = await getDoc(userDocRef);
      return userDoc.exists();
    } catch (error) {
      console.error('Error checking user document existence:', error);
      return false;
    }
  }

  /**
   * Create user document manually (fallback if function fails)
   * Note: This should normally be handled by the Firebase Function trigger
   */
  static async createUserDocument(authUser: AuthUser, role: UserRole = UserRole.USER): Promise<void> {
    try {
      const userDocRef = doc(db, 'users', authUser.uid);
      
      // Check if document already exists
      const existingDoc = await getDoc(userDocRef);
      if (existingDoc.exists()) {
        console.log('User document already exists');
        return;
      }

      const userDoc: Omit<UserDocument, 'createdAt' | 'updatedAt'> & {
        createdAt: Timestamp;
        updatedAt: Timestamp;
      } = {
        uid: authUser.uid,
        email: authUser.email || '',
        displayName: authUser.displayName,
        photoURL: authUser.photoURL,
        role,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        isActive: true,
        metadata: {
          lastLoginAt: Timestamp.now(),
          emailVerified: authUser.emailVerified,
          creationMethod: 'email' // This could be determined better
        }
      };

      await setDoc(userDocRef, userDoc);
      console.log('User document created successfully');
    } catch (error) {
      console.error('Error creating user document:', error);
      throw error;
    }
  }

  /**
   * Health check for Firebase Functions
   */
  static async healthCheck(): Promise<{ status: string; timestamp: string; version: string }> {
    try {
      const healthCheckFunction = httpsCallable(functions, 'healthCheck');
      const result = await healthCheckFunction();
      return result.data as { status: string; timestamp: string; version: string };
    } catch (error) {
      console.error('Error performing health check:', error);
      throw error;
    }
  }

  /**
   * Get all users (admin only - direct Firestore query)
   */
  static async getAllUsers(): Promise<UserDocument[]> {
    try {
      // Query all users from Firestore, ordered by creation date (newest first)
      const usersRef = collection(db, 'users');
      const usersQuery = query(usersRef, orderBy('createdAt', 'desc'));
      const usersSnapshot = await getDocs(usersQuery);
      
      // Process the users data to handle timestamps consistently
      const processedUsers: UserDocument[] = usersSnapshot.docs.map(doc => {
        const userData = doc.data();
        
        return {
          uid: userData.uid,
          email: userData.email,
          displayName: userData.displayName,
          photoURL: userData.photoURL,
          role: userData.role,
          isActive: userData.isActive,
          createdAt: this.processTimestamp(userData.createdAt) || new Date().toISOString(),
          updatedAt: this.processTimestamp(userData.updatedAt) || new Date().toISOString(),
          metadata: {
            lastLoginAt: this.processTimestamp(userData.metadata?.lastLoginAt),
            emailVerified: userData.metadata?.emailVerified || false,
            creationMethod: userData.metadata?.creationMethod || 'other'
          }
        } as UserDocument;
      });
      
      return processedUsers;
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }
} 