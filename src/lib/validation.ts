import { z } from 'zod';

const checkStatusSchema = z.enum(['OK', 'Faulty', 'N/A', 'Missing']);

export const generalInfoSchema = z.object({
  clientName: z.string().min(1, 'Client name is required'),
  building: z.string().min(1, 'Building is required'),
  inspectedBy: z.string().min(1, 'Inspector name is required'),
  approvedBy: z.string().min(1, 'Approver name is required'),
  date: z.string().min(1, 'Date is required'),
  ppmAttempt: z.number().min(1, 'PPM attempt must be at least 1'),
  equipmentName: z.string().min(1, 'Equipment name is required'),
  location: z.string().min(1, 'Location is required'),
  tagNo: z.string().min(1, 'Tag number is required'),
});

export const mechanicalCheckSchema = z.object({
  airflowVelocity: z.number().optional(),
  beltWearPulleyAlignment: checkStatusSchema,
  bladeImpellerDamage: checkStatusSchema,
  boltSetScrewTightness: checkStatusSchema,
  bladeTipClearance: checkStatusSchema,
  excessiveVibration: checkStatusSchema,
  fanGuardProtection: checkStatusSchema,
  fanPowerOff: checkStatusSchema,
  motorOverheating: checkStatusSchema,
  rotationDirection: checkStatusSchema,
  cleanBladesHousing: checkStatusSchema,
  dustDebrisRemoval: checkStatusSchema,
  erraticOperation: checkStatusSchema,
  inletVanesOperation: checkStatusSchema,
  bearingLubrication: checkStatusSchema,
  noObstructionsBackflow: checkStatusSchema,
  physicalDamageStability: checkStatusSchema,
  speedRpm: z.number().optional(),
  springMountVibrationIsolator: checkStatusSchema,
  unusualSoundDecibel: z.number().optional(),
});

export const electricalCheckSchema = z.object({
  bmsControlsInterlocks: checkStatusSchema,
  burntMarksDiscolorMelted: checkStatusSchema,
  circuitBreakerFunctional: checkStatusSchema,
  contractorsBreakers: checkStatusSchema,
  fireAlarmConnected: checkStatusSchema,
  fuseTerminals: checkStatusSchema,
  mccPowerOffBreaker: checkStatusSchema,
  signsLiquidLeaks: checkStatusSchema,
  tripSettingsFunction: checkStatusSchema,
  controlRelaysOperations: checkStatusSchema,
  currentAmps: z.number().optional(),
  doorsCoversCloseProperly: checkStatusSchema,
  frayingExposedWires: checkStatusSchema,
  highLowSpeedVerification: checkStatusSchema,
  indicationsOnOffTrip: checkStatusSchema,
  looseWiresToBeTightened: checkStatusSchema,
  motorPowerKw: z.number().optional(),
  potentialVoltage: z.number().optional(),
  selectorHandStopAuto: checkStatusSchema,
  testEmergencyStopButton: checkStatusSchema,
});

export const sequenceControlsCheckSchema = z.object({
  dptDifferentialPressureTransmitter: checkStatusSchema,
  erraticOperationMalfunctioning: checkStatusSchema,
  indicationsOnOffTrip: checkStatusSchema,
  mccOffOverrideFunction: checkStatusSchema,
  msfdDamperFunctional: checkStatusSchema,
  offWithDuctDetectorActivation: checkStatusSchema,
  overrideFscsPanelStatus: checkStatusSchema,
  sameTagNameInMccFan: checkStatusSchema,
  selectorRunStopAuto: checkStatusSchema,
  vfdVariableFrequencyDrive: checkStatusSchema,
});

export const checklistSchema = z.object({
  generalInfo: generalInfoSchema,
  mechanicalChecks: mechanicalCheckSchema,
  electricalChecks: electricalCheckSchema,
  sequenceControlsChecks: sequenceControlsCheckSchema,
  remarks: z.string(),
  beforeImage: z.string().optional(),
  afterImage: z.string().optional(),
  inspectorSignature: z.string().optional(),
});

// Equipment tag validation schema
export const equipmentTagSchema = z.object({
  clientName: z.string().min(1, 'Client name is required').max(100, 'Client name is too long'),
  equipmentName: z.string().min(1, 'Equipment name is required').max(100, 'Equipment name is too long'),
  tagNumber: z.string().min(1, 'Tag number is required').max(50, 'Tag number is too long'),
  building: z.string().min(1, 'Building is required').max(100, 'Building name is too long'),
  location: z.string().min(1, 'Location is required').max(100, 'Location is too long'),
});

export type ChecklistFormData = z.infer<typeof checklistSchema>;
export type EquipmentTagFormData = z.infer<typeof equipmentTagSchema>; 