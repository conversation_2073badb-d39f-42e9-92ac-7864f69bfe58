import { Timestamp } from 'firebase/firestore';

// Equipment tag interface for Firestore
export interface EquipmentTag {
  id: string;
  contractor: 'Auburn Engineering WLL';
  clientName: string;
  equipmentName: string;
  tagNumber: string;
  dateOfCreation: string; // ISO string format
  building: string;
  location: string;
  qrCodeData: string; // Base64 encoded QR code image
  createdAt: Timestamp | string;
  updatedAt: Timestamp | string;
  createdBy: string; // admin user ID who created this tag
}

// Form data interface for creating/editing equipment tags
export interface EquipmentTagFormData {
  clientName: string;
  equipmentName: string;
  tagNumber: string;
  building: string;
  location: string;
}

// QR code content structure
export interface QRCodeContent {
  contractor: string;
  clientName: string;
  equipmentName: string;
  tagNumber: string;
  dateOfCreation: string;
  building: string;
  location: string;
}

// Helper function to format QR code content as plain text
export function formatQRCodeContent(data: QRCodeContent): string {
  return `Contractor: ${data.contractor}
Client: ${data.clientName}
Equipment: ${data.equipmentName}
Tag: ${data.tagNumber}
Date: ${data.dateOfCreation}
Building: ${data.building}
Location: ${data.location}`;
}

// Helper function to generate filename for QR code download
export function generateQRCodeFilename(tagNumber: string): string {
  // Clean tag number and use it as document name
  const cleanTagNumber = tagNumber
    .replace(/[^a-zA-Z0-9]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '')
    .toUpperCase();
  
  return `${cleanTagNumber}_QR_CODE`;
} 